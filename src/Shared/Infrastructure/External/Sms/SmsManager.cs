using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Shared.Domain;

namespace Shared.Infrastructure.External.Sms;

public partial class SmsManager(
    AppSettings appSettings,
    ILogger<SmsManager> logger
) : ISmsManager
{
    [GeneratedRegex("[^0-9]+")]
    private static partial Regex MyRegex();

    public async Task SendSmsAsync(string Phone, string Message)
    {
        string phone = MyRegex().Replace(Phone, string.Empty);
        if (appSettings.SmsProvider == "1Telekom")
        {
            await Send1TelekomAsync(phone, Message);
        }
        else if (appSettings.SmsProvider == "GesTelekom")
        {
            await SendGesTelekomAsync(phone, Message);
        }
    }

    private async Task Send1TelekomAsync(string Phone, string Message)
    {
        string xml = $"""
                    <?xml version="1.0" encoding="UTF-8"?>
                    <Submit xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="SmsApi">
                        <Credential>
                            <Password>{appSettings.SmsPassword}</Password>
                            <Username>{appSettings.SmsUserName}</Username>
                        </Credential>
                        <DataCoding>Turkish</DataCoding>
                        <Header>
                            <From>{appSettings.SmsUserBaslik}</From>
                            <ScheduledDeliveryTime></ScheduledDeliveryTime>
                            <ValidityPeriod>0</ValidityPeriod>
                        </Header>
                        <Message>{Message}</Message>
                        <To xmlns:d2p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
                            <d2p1:string>{Phone}</d2p1:string>
                        </To>
                    </Submit>
                    """;
        using var client = new HttpClient();
        client.DefaultRequestHeaders.Add("ContentType", "application/x-www-form-urlencoded");
        var content = new StringContent(xml, Encoding.UTF8, "application/x-www-form-urlencoded");
        var response = await client.PostAsync("http://panel.1telekom.com.tr/Api/Submit", content);
        var responseString = await response.Content.ReadAsStringAsync();
    }

    private async Task SendGesTelekomAsync(string Phone, string Message)
    {
        var client = new HttpClient();
        var loginData = new
        {
            userName = appSettings.SmsUserName,
            password = appSettings.SmsPassword
        };
        var tokenResponse = await client.PostAsJsonAsync("https://restapi.ttmesaj.com/api/Login/TokenJson", loginData);
        if (!tokenResponse.IsSuccessStatusCode)
        {
            var error = await tokenResponse.Content.ReadAsStringAsync();
            logger.LogError("Ges Telekom token error: {error}", error);
            return;
        }
        var tokenResponseResult = await tokenResponse.Content.ReadFromJsonAsync<TokenResponse>();
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenResponseResult.access_token);
        var requestData = new
        {
            username = appSettings.SmsUserName,
            password = appSettings.SmsPassword,
            numbers = Phone,
            message = Message,
            origin = appSettings.SmsUserBaslik,
            sd = "0",
            ed = "0",
            isNotification = false,
            recipentType = "BIREYSEL",
            brandCode = appSettings.SmsCompanyCode
        };
        var response = await client.PostAsJsonAsync("https://restapi.ttmesaj.com/api/SendSms/SendSingle", requestData);
        if (!response.IsSuccessStatusCode)
        {
            var error = await response.Content.ReadAsStringAsync();
            logger.LogError("Ges Telekom send error: {error}", error);
            return;
        }
        var smsResponseResult = await tokenResponse.Content.ReadFromJsonAsync<SmsResponse>();
        if (smsResponseResult?.message != "*OK*")
        {
            var error = await response.Content.ReadAsStringAsync();
            logger.LogError("Ges Telekom send error: {error}", error);
            return;
        }
    }
}
public class TokenResponse
{
    public string access_token { get; set; }
}

public class SmsResponse
{
    public string sonuc { get; set; }
    public string kontor { get; set; }
    public string message { get; set; }
}
