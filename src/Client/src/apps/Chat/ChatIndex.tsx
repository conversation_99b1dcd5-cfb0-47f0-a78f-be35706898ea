import { Col, Row } from "antd";
import UserListIndex from "./Components/LeftUserList/UserListIndex";
import MessageDetailsIndex from "./Components/RightMessageDetails/MessageDetailsIndex";
import TopOptions from "./Components/TopOptions";
import { useQueryClient } from "react-query";
import { FC, useEffect } from "react";
import endPoints from "./EndPoints";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import UnSelectChat from "./Components/UnSelectChat";
import Title from "./Components/Title";
import { useParams, useSearchParams } from "react-router-dom";
import { hanldleSetUserChatFilter } from "./ClientSideStates";

const ChatIndex: FC<{ pageType: "customer" | "chat" | "ticket" }> = ({
  pageType,
}) => {
  const queryClient = useQueryClient();
  const { selectedChatItem } = useSelector((state: RootState) => state.chat);
  const [searchParams] = useSearchParams();
  const params = useParams();
  const customerId =
    searchParams.get("customerId") || params["customerId"] || "";
  const { userChatFilter: filter } = useSelector(
    (state: RootState) => state.chat
  );
  const dispatch = useDispatch();
  useEffect(() => {
    queryClient.resetQueries({
      queryKey: endPoints.getUserChatListFilter,
      exact: false,
    });
  }, []);

  useEffect(() => {
    const currentFilter = { ...filter };
    if (pageType === "customer" && customerId) {
      currentFilter["customerId"] = customerId;
    } else if (pageType === "chat" && !customerId) {
      delete currentFilter["customerId"];
    }
    dispatch(hanldleSetUserChatFilter({ filter: currentFilter }));
  }, [pageType]);

  return (
    <Row className="!fixed" style={{ width: "-webkit-fill-available" }}>
      {!customerId && pageType !== "ticket" && (
        <Col xs={24}>
          <Title />
        </Col>
      )}
      {pageType !== "ticket" && (
        <>
          <Col xs={24}>
            <TopOptions />
          </Col>

          <Col xs={24} xl={6}>
            <UserListIndex />
          </Col>
        </>
      )}

      <Col
        xs={24}
        xl={pageType === "ticket" ? 24 : 18}
        className="flex flex-col border-l border-gray-300"
      >
        {selectedChatItem ? <MessageDetailsIndex /> : <UnSelectChat />}
      </Col>
    </Row>
  );
};

export default ChatIndex;
