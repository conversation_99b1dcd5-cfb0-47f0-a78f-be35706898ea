import { FC } from "react";
import { Typography } from "antd";
import { EnvironmentOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { GoogleM<PERSON>, Marker, useJsApiLoader } from "@react-google-maps/api";
import MessageDate from "./MessageDate";
import MessageItemOptions from "./MessageItemOptions";

interface LocationItemProps {
  item: any;
}

const GOOGLE_MAP_LIBRARIES: any = ["places"];

const containerStyle = {
  width: "100%",
  height: "200px",
};

// Sabit koordinatları buradan yönet
const LOCATION = { lat: 38.6942783, lng: 35.5174819 };

const LocationItem: FC<LocationItemProps> = ({ item }) => {
  const { Text } = Typography;
  const { t } = useTranslation();

  const { isLoaded } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: (import.meta as any).env?.VITE_GOOGLE_MAP_API || "",
    libraries: GOOGLE_MAP_LIBRARIES,
    language:"tr"
  });

  // Google Maps'te yol tarifi aç
  const openInGoogleMaps = () => {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${LOCATION.lat},${LOCATION.lng}`;
    window.open(url, "_blank");
  };

  return (
    <>
        {item.Direction === "Incoming" && (
          <div className={`!flex justify-end !w-full !absolute right-0 top-1`}>
            <MessageItemOptions item={item} />
          </div>
        )}
      <div
        className="!relative !h-48 !rounded-lg !overflow-hidden !bg-gray-100 cursor-pointer !mb-2 !w-[270px]"
        onClick={openInGoogleMaps} // <-- Harita alanına tıklanınca çalışsın
      >
       
        {isLoaded ? (
          <GoogleMap
            mapContainerStyle={containerStyle}
            center={LOCATION}
            zoom={12}
            options={{
              streetViewControl: false,
              mapTypeControl: false,
              fullscreenControl: false,
              zoomControl: false,
              disableDefaultUI: true,
            }}
          >
            <Marker position={LOCATION} />
          </GoogleMap>
        ) : (
          <div className="!flex flex-col items-center justify-center !h-full !text-gray-500">
            <EnvironmentOutlined className="!text-4xl !mb-2" />
            <Text className="!text-sm">{t("chat.mapLoadError")}</Text>
          </div>
        )}
      </div>

      <MessageDate item={item} />
    </>
  );
};

export default LocationItem;
