import { FC, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Button, Drawer, Dropdown, MenuProps, message } from "antd";
import {
  CopyOutlined,
  DownloadOutlined,
  EnvironmentOutlined,
  MoreOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import { handleSetSelectedChatMessageItem } from "../../ClientSideStates";
import { useTranslation } from "react-i18next";
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";
import { useSearchParams } from "react-router-dom";
import { RootState } from "@/store/Reducers";
import { useGetChatMessageDetails } from "../../ServerSideStates";
import AddOrUpdateIndex from "@/apps/Admin/Pages/Ticket/Components/AddOrUpdateIndex";

const MessageItemOptions: FC<{ item: any }> = ({ item }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [isShowAddTicketDrawer, setIsShowAddTicketDrawer] = useState(false);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { selectedChatItem } = useSelector((state: RootState) => state.chat);
  const chatDetails = useGetChatMessageDetails(selectedChatItem?.Id);
  const handleSelectMessage = () => {
    dispatch(handleSetSelectedChatMessageItem({ data: item }));
  };

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(item.Content);
      message?.success(t("chat.messageHasBeenCopied"));
    } catch (error) {
      console.error(t("chat.copyFaild"), error);
    }
  };

  const handleDownloadFile = async () => {
    const fileUrl = `${setBackEndUrl()}/Uploads/${
      item?.Attachments[0]?.FilePath
    }`;
    const fileName = item?.Attachments[0]?.FileName;
    try {
      const response = await fetch(fileUrl);
      if (!response.ok) throw new Error("Dosya indirilemedi");

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = url;
      a.download = fileName; // örn: "image.png" veya "document.pdf"
      document.body.appendChild(a);
      a.click();
      a.remove();

      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("İndirme hatası:", error);
    }
  };

  const items: MenuProps["items"] = (() => {
    if (item.ContentType === "Text") {
      return [
        {
          key: "copy",
          icon: <CopyOutlined />,
          label: t("chat.copy"),
          onClick: handleCopyToClipboard,
        },
        {
          key: "reply",
          icon: <SyncOutlined />,
          label: t("chat.reply"),
          onClick: handleSelectMessage,
        },
      ];
    }

    if (
      item.ContentType === "Location" &&
      chatDetails?.data?.Value?.CustomerId
    ) {
      return [
        {
          key: "createTicket",
          icon: <EnvironmentOutlined />,
          label: t("chat.createTicket"),
          onClick: () => {
            const splitLoc = item?.Content?.includes(",")
              ? item?.Content?.split(",")
              : [];
            setSearchParams({
              externalLat: splitLoc[0],
              externalLng: splitLoc[1],
              customerId: chatDetails?.data?.Value?.CustomerId,
              ticketChatId: chatDetails?.data?.Value?.Id,
            });
            setIsShowAddTicketDrawer(true);
          },
        },
      ];
    }

    return [
      {
        key: "download",
        icon: <DownloadOutlined />,
        label: t("chat.download"),
        onClick: handleDownloadFile,
      },
    ];
  })();

  const handleTicketSubmit = () => {
    // DOM'dan form'u bulup submit et
    const formElement = document.querySelector(
      ".ticket-form form"
    ) as HTMLFormElement;
    if (formElement) {
      formElement.requestSubmit();
    }
  };
  return (
    <>
      <Dropdown menu={{ items }} trigger={["click"]}>
        <MoreOutlined className="text-black !text-base  cursor-pointer" />
      </Dropdown>
      <Drawer
        open={isShowAddTicketDrawer}
        onClose={() => {
          setIsShowAddTicketDrawer(false);

          setSearchParams({});
        }}
        title={
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              width: "100%",
            }}
          >
            <span>{t("ticket.list.addTicket")}</span>
            <Button
              type="primary"
              onClick={handleTicketSubmit}
              style={{ marginRight: "16px" }}
            >
              {t("ticket.list.add")}
            </Button>
          </div>
        }
        width={"80%"}
        bodyStyle={{ padding: 0 }}
      >
        <AddOrUpdateIndex />
      </Drawer>
    </>
  );
};

export default MessageItemOptions;
