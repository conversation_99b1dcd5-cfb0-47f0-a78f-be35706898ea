import { RootState } from "@/store/Reducers";
import { TagsOutlined, UserAddOutlined } from "@ant-design/icons";
import { Col, Drawer, Row, Tooltip, Typography, Button } from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useGetChatMessageDetails } from "../../ServerSideStates";
import { useState } from "react";
import AddOrUpdateCustomerIndex from "@/apps/Admin/Pages/Customers/Components/AddOrUpdate/AddOrUpdateCustomerIndex";
import { useSearchParams } from "react-router-dom";
import AddOrUpdateIndex from "@/apps/Admin/Pages/Ticket/Components/AddOrUpdateIndex";
import { hanldleSetTicketDetails } from "@/apps/Admin/Pages/Ticket/ClientSideStates";

const Header = () => {
  const { selectedChatItem } = useSelector((state: RootState) => state.chat);
  const chatDetails = useGetChatMessageDetails(selectedChatItem?.Id);
  const { t } = useTranslation();
  const { Text } = Typography;
  const [searchParams, setSearchParams] = useSearchParams();
  const [isShowCustomerDrawer, setIsShowCustomerDrawer] = useState(false);
  const [isShowTicketDrawer, setIsShowTicketDrawer] = useState(false);
  const [mode, setMode] = useState<"customer" | "ticket" | null>(null);
  let name = chatDetails?.data?.Value?.CustomerName || "";
  const dispatch = useDispatch()

  
  const handleTicketSubmit = () => {
    // DOM'dan form'u bulup submit et
    const formElement = document.querySelector(
      ".ticket-form form"
    ) as HTMLFormElement;
    if (formElement) {
      formElement.requestSubmit();
    }
  };

  const initials = name
    .split(" ")
    .map((n: any) => n[0])
    .join("")
    .toUpperCase();

  return (
    <Row>
      <Col xs={24}>
        <Row>
          <Col xs={12}>
            <div className="!flex items-center gap-2">
              <div className="!w-[60px] !h-[50px] !bg-[#58666e] !flex items-center justify-center !cursor-pointer !relative">
                <Text className="!text-lg !text-white">{initials}</Text>
              </div>
              <div className="!flex flex-col gap-1 ">
                <Text>{name}</Text>
                <Text className="!text-xs !text-gray-600">
                  {chatDetails?.data?.Value?.ExternalId}
                </Text>
              </div>
            </div>
          </Col>
          <Col xs={12} className="!flex justify-end items-center gap-2 px-4">
            <Tooltip
              title={
                chatDetails?.data?.Value?.CustomerId
                  ? t("chat.createTicket")
                  : t("chat.createCustomerAndCreateTicket")
              }
            >
              <TagsOutlined
                className="!text-[#b5b5b5] !text-2xl cursor-pointer"
                onClick={async () => {
                  await setMode("ticket");
                  dispatch(hanldleSetTicketDetails({data:null}))
                  if (chatDetails?.data?.Value?.CustomerId) {
                    setSearchParams({
                      customerId: chatDetails?.data?.Value?.CustomerId,
                      ticketChatId: chatDetails?.data?.Value?.Id,
                    });
                    setIsShowTicketDrawer(true);
                  } else {
                    setSearchParams({
                      chatCustomerName: name?.replace("~", ""),
                      chatCustomerPhone: chatDetails?.data?.Value?.ExternalId,
                      ticketChatId: chatDetails?.data?.Value?.Id,
                    });
                    
                    setIsShowCustomerDrawer(true);
                  }
                }}
              />
            </Tooltip>
            {!chatDetails?.data?.Value?.CustomerId && (
              <Tooltip title={t("chat.createAsCustomer")}>
                <UserAddOutlined
                  onClick={async () => {
                    await setMode("customer");
                    setSearchParams({
                      chatCustomerName: name?.replace("~", ""),
                      chatCustomerPhone: chatDetails?.data?.Value?.ExternalId,
                      ticketChatId: chatDetails?.data?.Value?.Id,
                    });
                    setIsShowCustomerDrawer(true);
                  }}
                  className="!text-[#b5b5b5] !text-2xl cursor-pointer"
                />
              </Tooltip>
            )}
          </Col>
        </Row>
      </Col>
      <Drawer
        open={isShowCustomerDrawer}
      closeIcon={null}
        onClose={() => {
          if (mode === "ticket") {
            if (searchParams.get("customerId")) {
              setIsShowCustomerDrawer(false);
              setIsShowTicketDrawer(true);
            } else {
              setIsShowCustomerDrawer(false);
              setIsShowTicketDrawer(false);
              setSearchParams({});
            }
          } else {
            setIsShowCustomerDrawer(false);
            setSearchParams({});
          }

          setMode(null);
        }}
        width={"80%"}
      >
        <AddOrUpdateCustomerIndex type="drawer" 
         onFinish={() => {
          if (mode === "ticket") {
            if (searchParams.get("customerId")) {
              setIsShowCustomerDrawer(false);
              setIsShowTicketDrawer(true);
            } else {
              setIsShowCustomerDrawer(false);
              setIsShowTicketDrawer(false);
              setSearchParams({});
            }
          } else {
            setIsShowCustomerDrawer(false);
            setSearchParams({});
          }

          setMode(null);
        }}
        />
      </Drawer>

      <Drawer
        open={isShowTicketDrawer}
        onClose={() => {
          setIsShowTicketDrawer(false);

          setSearchParams({});
        }}
        title={
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              width: "100%",
            }}
          >
            <span>{t("ticket.list.addTicket")}</span>
            <Button
              type="primary"
              onClick={handleTicketSubmit}
              style={{ marginRight: "16px" }}
            >
              {t("ticket.list.add")}
            </Button>
          </div>
        }
        width={"80%"}
        bodyStyle={{ padding: 0 }}
      >
        <AddOrUpdateIndex />
      </Drawer>
    </Row>
  );
};

export default Header;
