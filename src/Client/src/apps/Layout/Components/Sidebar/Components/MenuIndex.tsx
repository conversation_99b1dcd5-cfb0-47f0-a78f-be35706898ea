import { Typography } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import AdminMenuButton from "./AdmnMenuButton";
import { useGetAdminMenus } from "../ServerSideStates";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { allMenuIcons } from "./AllMenuIcons";
import { adminMenuItems } from "./AdminMenuItems";
import { commonRoutePrefix } from "../../../../../routes/Prefix";
import { handleResetAllFieldsChat } from "@/apps/Chat/ClientSideStates";

const MenuIndex = () => {
  const { Text } = Typography;
  const navigate = useNavigate();
  const { t } = useTranslation();
  const location = useLocation();
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const [data, setData] = useState<any[]>([]);
  const menus = useGetAdminMenus();
 const dispatch = useDispatch()


  const findIconByUrl = (url: string) => {
    const findIcon = allMenuIcons.find((item: any) =>
      item.url === "/settings/general"
        ? item.url === url
        : commonRoutePrefix + item.url === url
    );
    if (findIcon) {
      return findIcon?.icon;
    }
  };

  useEffect(() => {
    const menuItems = adminMenuItems(t);

    if (userInfoes?.Role?.toLowerCase() === "admin") {
      
      const fullPathWithParams = location.pathname + location.search;

      const withStatus = menuItems.map((item: any) => {
        return {
          ...item,
          status: item.url === fullPathWithParams,
        };
      });

      setData(withStatus);
    } else {
      if (menus?.data?.Value) {
        const withStatus = [];

        const staticMenus = [menuItems[0], menuItems[1], menuItems[2]].map(
          (item: any) => ({
            ...item,
            status: item.url === location.pathname,
          })
        );

        withStatus.push(staticMenus[0], staticMenus[1]);

        const dynamicMenus = menus?.data?.Value?.filter(
          (item: any) => item.IsMenu
        ).map((item: any) => ({
          title: item.Name,
          url: item.Url,
          icon: item.Icon,
          status: item.Url === location.pathname,
        }));

        if (dynamicMenus) {
          withStatus.push(...dynamicMenus);
        }

        withStatus.push(staticMenus[2]);
        setData(withStatus);
      }
    }
  }, [location.pathname, t, userInfoes, menus.data]);

  const handleClick = (url: string) => {
    
    if(url==="/panel/professions?type=constant"){
      localStorage.setItem("selectedParent","jobs")
    }
    else if(url==="/panel/chat")
    {
      dispatch(handleResetAllFieldsChat())
    }
    
    navigate(url);
  };

  return (
    <div className="!flex flex-col !min-h-screen">
      {data.map((item, index) => (
        <div
          key={index}
          className={`!w-full !h-[40px] !flex !flex-col items-center justify-center !cursor-pointer  !my-1 ${
            item.status ? "!bg-[#0096d1]" : ""
          }`}
          onClick={() => handleClick(item.url)}
        >
          <div className="!mt-1">{findIconByUrl(item?.url || item?.Url)}</div>

          <div>
            <Text
              className={`!text-[10px] ${
                item.status ? "!text-white" : "!text-[#b5b5b5]"
              }`}
            >
              {item.title}
            </Text>
          </div>
        </div>
      ))}
      <div className="!absolute bottom-16 !w-full">
        <AdminMenuButton />
      </div>
    </div>
  );
};

export default MenuIndex;
