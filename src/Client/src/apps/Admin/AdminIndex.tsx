import { Col, Row } from "antd";
import TreeMenu from "../Common/TreeMenu";
import { useEffect, useState } from "react";
import { Outlet, useLocation, } from "react-router-dom";
import {
  AimOutlined,
  Bar<PERSON>hartOutlined,
  BellOutlined,
  BookOutlined,
  BranchesOutlined,
  CalendarOutlined,
  ClusterOutlined,
  ContainerOutlined,
  DiffOutlined,
  FolderAddOutlined,
  FormOutlined,
  ImportOutlined,
  KeyOutlined,
  MessageOutlined,
  PauseCircleOutlined,
  PhoneOutlined,
  ProductOutlined,
  ProfileOutlined,
  SolutionOutlined,
  TagsOutlined,
  TeamOutlined,
  UnorderedListOutlined,
  UsergroupAddOutlined,
  UserOutlined,
  UserSwitchOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { handleResetAllFieldsCustomer } from "./Pages/Customers/ClientSideStates";
import { handleResetAllFieldsTicket } from "./Pages/Ticket/ClientSideStates";
import { RootState } from "@/store/Reducers";
import ValidateUserPermission from "@/routes/PermissionGuard";
import { handleResetFilterNotes } from "./Pages/Notes/ClientSideStates";
import { handleResetAllFieldsChat } from "../Chat/ClientSideStates";

const AdminIndex = () => {
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);

  const commonRoutePrefix = "/panel";

  const allMenus = [
    {
      key: "customers",
      title: t("adminSidebar.customers"),
      url: `${commonRoutePrefix}/customers`,
      icon: <UsergroupAddOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "tempCustomers",
      title: t("tempCustomer.list.tempCustomers"),
      url: `${commonRoutePrefix}/import-data`,
      icon: <ImportOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "autoDailer",
      title: t("adminSidebar.autoDialer"),
      url: `${commonRoutePrefix}/auto-dailer`,
      icon: <PhoneOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "pauses",
      title: t("adminSidebar.pauseManagement"),
      url: `${commonRoutePrefix}/pauses`,
      icon: <PauseCircleOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "pauseType",
      title: t("pauseType.pauseTypes"),
      url: `${commonRoutePrefix}/pause-types`,
      icon: <PauseCircleOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "tickets",
      title: t("adminSidebar.notification&Tasks"),
      url: `${commonRoutePrefix}/tickets`,
      icon: <TagsOutlined className="!text-base !text-[#909090]" />,
    },
    // {
    //   key: "tasks",
    //   title: t("adminSidebar.task"),
    //   url: `${commonRoutePrefix}/tasks`,
    //   icon: <ProfileOutlined className="!text-base !text-[#909090]" />,
    // },
    {
      key: "callReports",
      title: t("adminSidebar.callReports"),
      url: `${commonRoutePrefix}/call-reports`,
      icon: <BarChartOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "recording",
      title: t("adminSidebar.recordings"),
      url: `${commonRoutePrefix}/recordings`,
      icon: <AimOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "subjectTickets",
      title: t("adminSidebar.subjectTickets"),
      url: `${commonRoutePrefix}/subject-ticket`,
      icon: <DiffOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "users",
      title: t("adminSidebar.users"),
      url: `${commonRoutePrefix}/users`,
      icon: <UserOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "department",
      title: t("adminSidebar.department"),
      url: `${commonRoutePrefix}/user-department`,
      icon: <UsergroupAddOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "jobs",
      title: t("adminSidebar.profession"),
      url: `${commonRoutePrefix}/professions`,
      icon: <SolutionOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "sectors",
      title: t("adminSidebar.sectors"),
      url: `${commonRoutePrefix}/sectors`,
      icon: <ProductOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "customerGroup",
      title: t("adminSidebar.classification"),
      url: `${commonRoutePrefix}/classifications`,
      icon: <TeamOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "customerSource",
      title: t("adminSidebar.customerSource"),
      url: `${commonRoutePrefix}/customer-sources`,
      icon: <BookOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "fileManager",
      title: t("adminSidebar.fileManager"),
      url: `${commonRoutePrefix}/file-manager`,
      icon: <FolderAddOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "authority",
      title: t("adminSidebar.authority"),
      url: `${commonRoutePrefix}/authority`,
      icon: <UserSwitchOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "role",
      title: t("adminSidebar.roles"),
      url: `${commonRoutePrefix}/roles`,
      icon: <KeyOutlined className="!text-base !text-[#909090]" />,
    },
    // {
    //   key: "language",
    //   title: t("adminSidebar.languages"),
    //   url: `${commonRoutePrefix}/languages`,
    //   icon: <FlagFilled className="!text-base !text-[#909090]" />,
    // },
    {
      key: "workflow",
      title: t("adminSidebar.workflow"),
      url: `${commonRoutePrefix}/workflow`,
      icon: <BranchesOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "notificationWays",
      title: t("notificationWay.notificationWays"),
      url: `${commonRoutePrefix}/notification-ways`,
      icon: <BellOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "auditLogs",
      title: t("auditLog.logs"),
      url: `${commonRoutePrefix}/logs`,
      icon: <ClusterOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "threecxqueues",
      title: t("threecxqueues.queues"),
      url: `${commonRoutePrefix}/threecx-queues`,
      icon: <UnorderedListOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "notes",
      title: t("notes.note"),
      url: `${commonRoutePrefix}/notes`,
      icon: <MessageOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "template",
      title: t("template.templates"),
      url: `${commonRoutePrefix}/templates`,
      icon: <ContainerOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "calendar",
      title: t("adminSidebar.calendar"),
      url: `${commonRoutePrefix}/calendar-notes`,
      icon: <CalendarOutlined className="!text-base !text-[#909090]" />,
    },
    {
      key: "forms",
      title: t("adminSidebar.form"),
      url: `${commonRoutePrefix}/forms`,
      icon: <FormOutlined className="!text-base !text-[#909090]" />,
    },
  ];

  const isConstantType = searchParams.get("type") === "constant";
  const visibleKeys = isConstantType
    ? ["jobs", "sectors", "customerGroup", "customerSource", "notificationWays"]
    : allMenus.map((m) => m.key).filter((key) => !["jobs", "sectors", "customerGroup", "customerSource", "notificationWays"].includes(key));

  const menuData = allMenus.filter((item) => visibleKeys.includes(item.key));

 

  const [selectedParent, setSelectedParent] = useState<any>(null);
  const [selectedChild, setSelectedChild] = useState<any>(null);

  useEffect(() => {
    if (
      selectedParent == "customers" ||
      selectedParent === "tempCustomers" ||
      "autoDailer"
    ) {
      dispatch(handleResetAllFieldsCustomer());
      dispatch(handleResetFilterNotes())
    }
    if (selectedParent == "tickets") {
      dispatch(handleResetAllFieldsTicket());
    }
    if( selectedParent == "notes" )
    {
      dispatch(handleResetFilterNotes())
    }
    
    
  }, [selectedParent]);

  return (
    <>
      {userInfoes?.Role?.toLowerCase() === "admin" ? (
        <>
          <Col xs={24}>
            <Row className="!flex">
              <div className="!w-[200px]">
                <TreeMenu
                  data={menuData}
                  selectedParent={selectedParent}
                  setSelectedParent={setSelectedParent}
                  selectedChild={selectedChild}
                  setSelectedChild={setSelectedChild}
                  defaultMenuKey={searchParams.get("type")?"jobs":"users"}
                />
              </div>
              <div className="w-[calc(100%-200px)]">
                <Outlet />
              </div>
            </Row>
          </Col>
        </>
      ) : (
        <>
          <ValidateUserPermission />
        </>
      )}
    </>
  );
};

export default AdminIndex;
