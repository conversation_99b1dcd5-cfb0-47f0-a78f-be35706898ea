import {
  DeleteOutlined,
  FormOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import {
  Col,
  Drawer,
  Modal,
  Row,
  Table,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import endPoints from "../EndPoints";
import { useQueryClient } from "react-query";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { FC, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useGetTickets } from "../ServerSideStates";
import {
  deleteTicket,
  getTicketForDetail,
  updateTicketWithPut,
} from "../Services";
import {
  hanldleSetTicketDetails,
  hanldleSetTicketFilter,
  handleResetAllFieldsTicket,
} from "../ClientSideStates";
import { determineTicketPriority } from "@/helpers/Ticket";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import dayjs from "dayjs";
import { excludeUnnesessaryKey } from "@/helpers/ExcludeUnNesessaryKey";
import AddOrUpdateIndex from "./AddOrUpdateIndex";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { triggerTicketFormSubmit } from "./WrapperTicketForm";
import relativeTime from "dayjs/plugin/relativeTime";
import "dayjs/locale/tr";
import ExpandableText from "@/apps/Common/TruncatedDesc";
import DepartmentCell from "./DepartmenttCell";
import { withColumnVisibility } from "@/apps/Common/WithColumnVisibility";
import { useSearchParams } from "react-router-dom";

dayjs.extend(relativeTime);

const ListItems: FC<{ topTicketId?: string | null }> = ({ topTicketId }) => {
  const { Text } = Typography;
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const { filter, pageType, ticketDetails } = useSelector(
    (state: RootState) => state.ticket
  );
  const tickets = useGetTickets(
    topTicketId
      ? { ...filter, TopTicketId: topTicketId }
      : excludeUnnesessaryKey(filter)
  );
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  dayjs.extend(relativeTime);
  dayjs.locale("tr");

  const TableWithColumnVisibility1 = withColumnVisibility("ticket_1")(Table);
  const TableWithColumnVisibility2 = withColumnVisibility("ticket_2")(Table);

  const isUserInWatchlist = (record: any) => {
    const selectedUserId = userInfoes?.Id;
    const currentWatchlist = record?.Watchlist || [];

    const currentUserIds = currentWatchlist.map(
      (user: any) => user.UserId || user.Id || user
    );

    return currentUserIds.includes(selectedUserId);
  };

  // Watchlist toggle fonksiyonu - ekleme/çıkarma
  const handleToggleWatchlist = async (record: any) => {
    try {
      const selectedUserId = userInfoes?.Id;
      const currentWatchlist = record?.Watchlist || [];

      const currentUserIds = currentWatchlist.map(
        (user: any) => user.UserId || user.Id || user
      );

      const isUserAlreadyInWatchlist = currentUserIds.includes(selectedUserId);

      let newWatchlist;
      let successMessage;

      if (isUserAlreadyInWatchlist) {
        // Kullanıcıyı watchlist'ten çıkar
        newWatchlist = currentUserIds.filter(
          (userId: string) => userId !== selectedUserId
        );
        successMessage = "Takip listesinden çıkarıldınız!";
      } else {
        // Kullanıcıyı watchlist'e ekle
        newWatchlist = [...currentUserIds, selectedUserId];
        successMessage = "Takip listesine eklendiniz!";
      }

      const updatedTicket = {
        Id: record.Id,
        Code: record.Code,
        Title: record.Title,
        Description: record.Description,
        SubjectId: record.SubjectId,
        CustomerId: record.CustomerId,
        UserId: record.UserId,
        StatusId: record.StatusId,
        Priority: record.Priority,
        EndDate: record.EndDate,
        NotificationWayId: record.NotificationWayId,
        TopTicketId: record.TopTicketId,
        Address: record.Address,
        AttributeData: record.AttributeData,
        TicketFiles: record.TicketFiles || [],
        Watchlist: newWatchlist,
        DepartmentIds:
          record.Departments?.map(
            (dept: any) => dept.DepartmentId || dept.Id
          ) || [],
      };

      await updateTicketWithPut(updatedTicket);

      // Verileri yenile
      queryClient.resetQueries({
        queryKey: endPoints.getTicketListFilter,
        exact: false,
      });

      openNotificationWithIcon("success", successMessage);
    } catch (error: any) {
      showErrorCatching(error, null, false, t);
    }
  };

  const columns = [
    {
      title: t("ticket.list.title"),
      dataIndex: "Code",
      key: "Code",
      width: "14%",

      sorter: (a: any, b: any) => a?.Title.localeCompare(b?.Title),
      render: (value: string, record: any) => {
        return (
          <div className="!flex gap-1 items-center">
            <Tooltip title={record?.StatusName}>
              <span
                className={`w-[12px] !h-[12px] ${
                  record?.Status?.Type === "Start"
                    ? "!bg-[#0096d1]"
                    : record?.Status?.Type === "End"
                    ? " !bg-[#35b214]"
                    : "!bg-gray-400"
                }`}
              ></span>
            </Tooltip>
            <ExpandableText
              title={t("ticket.list.title")}
              limit={topTicketId ? 15 : 18}
              text={record?.Title || ""}
              textClassName="!text-xs"
            />

            <Text className="!text-[10px] !text-gray-500">({value})</Text>
          </div>
        );
      },
    },
    {
      title: t("ticket.list.type"),
      dataIndex: "TicketType",
      key: "TicketType",
      width: "6%",
      sorter: (a:any, b:any) => a?.TicketType - b?.TicketType,
      render: (value: number) => {
        return (
          <>
            <Text className="!text-xs">
              {value===1?t("ticket.list.ticket"):t("ticket.list.task")}
            </Text>
          </>
        );
      },
    },

    {
      title: t("ticket.list.subject"),
      dataIndex: "SubjectName",
      key: "Subject",
      width: "10%",
      sorter: (a: any, b: any) => a?.SubjectName.localeCompare(b?.SubjectName),
      render: (value: string) => {
        return (
          <>
            <ExpandableText
              title={t("ticket.list.subject")}
              limit={topTicketId ? 10 : 25}
              text={value || ""}
              textClassName="!text-xs"
            />
          </>
        );
      },
    },
    {
      title: t("task.list.assignedUser"),
      dataIndex: "UserName",
      key: "UserName",
      width: "10%",
      sorter: (a: any, b: any) => a?.UserName.localeCompare(b?.UserName),
      render: (value: string) => {
        return (
          <>
            <ExpandableText
              title={t("task.list.assignedUser")}
              limit={15}
              text={value || ""}
              textClassName="!text-xs"
            />
          </>
        );
      },
    },
    ...(topTicketId
      ? []
      : [
          {
            title: t("ticket.list.customer"),
            dataIndex: "CustomerName",
            key: "CustomerName",
            width: "10%",
            sorter: (a: any, b: any) =>
              a?.CustomerName.localeCompare(b?.CustomerName),
            render: (value: string) => {
              return (
                <>
                  <ExpandableText
                    title={t("ticket.list.customer")}
                    limit={15}
                    text={value || ""}
                    textClassName="!text-xs"
                  />
                </>
              );
            },
          },
        ]),

    {
      title: t("task.list.priority"),
      dataIndex: "Priority",
      key: "Priority",
      width: "6%",
      sorter: (a: any, b: any) =>
        determineTicketPriority(a?.Priority, t)?.localeCompare(
          determineTicketPriority(b?.Priority, t)
        ),
      render: (value: number) => {
        return (
          <>
            <Text className="!text-xs">
              {determineTicketPriority(value, t)}
            </Text>
          </>
        );
      },
    },
    {
      title: t("ticket.list.insertDate"),
      dataIndex: "InsertDate",
      key: "InsertDate",
      width: "10%", // Width biraz artırıldı gecikme metni için
      render: (value: number) => {
        return (
          <>
            {value && (
              <Text className="!text-xs">
                {dayjs(value).format("YYYY-MM.DD HH:mm")}
              </Text>
            )}
          </>
        );
      },
    },
    {
      title: t("ticket.list.departments"),
      dataIndex: "Departments",
      key: "Departments",
      width: "10%",
      render: (value: any) => {
        return (
          <div className="">
            {value?.length > 0 && (
              <>
                <DepartmentCell departments={value} />
              </>
            )}
          </div>
        );
      },
    },

    {
      title: t("ticket.list.sslDate"),
      dataIndex: "InsertDate",
      key: "SLA",
      width: "8%",
      render: (value: number, record: any) => {
        const now = dayjs();
        const insertDate = dayjs(value);
        const slaMinutes = record?.SlaTime ?? 0; // Dakika cinsinden SLA

        // SLA yoksa normal tarih gösterimi
        if (!slaMinutes || slaMinutes <= 0) {
          const diffInDays = now.diff(insertDate, "days");
          const humanized =
            diffInDays < 1
              ? insertDate.fromNow(true) + " önce" // Örn: '5 saat önce'
              : `${diffInDays} gün önce`; // Örn: '3 gün önce'

          return <Text className="!text-xs mb-1">{`${humanized}`}</Text>;
        }

        // SLA var, bitiş tarihini hesapla
        const slaDueDate = insertDate.add(slaMinutes, "minute");
        const isOverdue = now.isAfter(slaDueDate);

        if (!isOverdue) {
          return (
            <div>
              <Tag
                color="green"
                className="!text-xs mb-1"
                style={{
                  marginTop: "2px",
                  fontSize: "10px",
                  padding: "1px 4px",
                }}
              >
                {`${slaDueDate.fromNow()}`}
              </Tag>
            </div>
          );
        } else {
          // SLA süresi dolmuş - Kırmızı tag ile gecikme gösterimi
          const overdueMinutes = now.diff(slaDueDate, "minute");
          const overdueDays = Math.floor(overdueMinutes / 1440);
          const overdueHours = Math.floor((overdueMinutes % 1440) / 60);
          const remainingMinutes = overdueMinutes % 60;

          let overdueText = "";

          if (overdueDays > 0) {
            overdueText = `${overdueDays} gün`;
            if (overdueHours > 0) {
              overdueText += ` ${overdueHours} saat`;
            }
          } else if (overdueHours > 0) {
            overdueText = `${overdueHours} saat`;
            if (remainingMinutes > 0) {
              overdueText += ` ${remainingMinutes} dk`;
            }
          } else {
            overdueText = `${remainingMinutes} dk`;
          }

          return (
            <div className="mb-1">
              <Tag
                color="red"
                className="!text-xs"
                style={{
                  marginTop: "2px",
                  fontSize: "10px",
                  padding: "1px 4px",
                }}
              >
                {`${overdueText}`}
              </Tag>
            </div>
          );
        }
      },
    },

    {
      title: t("ticket.list.subTickets"),

      key: "SubTickets",
      width: "10%",
      render: (value: any, record: any) => {
        return (
          <>
            {record?.SubTicketCount > 0 && (
              <div className="!flex items-center gap-1">
                <Text className="!text-[#35b214] !text-xs">
                  {record?.SubDoneTicketCount}
                </Text>
                <Text>/</Text>
                <Text className="!text-[#0096d1] text-xs ">
                  {record?.SubTicketCount}
                </Text>
              </div>
            )}
          </>
        );
      },
    },
    {
      title: "",
      dataIndex: "edit",
      key: "action",
      width: "8%",
      render: (key: any, record: any) => {
        const isWatching = isUserInWatchlist(record);

        return (
          <Col className="!flex gap-2 justify-end !px-2">
            {/* Watchlist toggle ikonu */}
            <Tooltip
              title={
                isWatching ? "Takip Listesinden Çıkar" : "Takip Listesine Ekle"
              }
            >
              {isWatching ? (
                <EyeInvisibleOutlined
                  className="!text-[#ff4d4f] !text-sm cursor-pointer hover:!text-[#ff7875]"
                  onClick={async (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    await handleToggleWatchlist(record);
                  }}
                />
              ) : (
                <EyeOutlined
                  className="!text-[#1890ff] !text-sm cursor-pointer hover:!text-[#40a9ff]"
                  onClick={async (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    await handleToggleWatchlist(record);
                  }}
                />
              )}
            </Tooltip>

            <Tooltip title={t("ticket.list.edit")}>
              <FormOutlined
                className="!text-[#0096d1] !text-sm cursor-pointer hover:!text-[#1890ff]"
                onClick={async (e) => {
                
                  e.preventDefault();
                  e.stopPropagation();

                  await dispatch(hanldleSetTicketDetails({ data: record }));

                  queryClient.resetQueries({
                    queryKey: endPoints.getTicketComments,
                    exact: false,
                  });
                  setIsShowEditDrawer(true);
                }}
              />
            </Tooltip>

            <Tooltip title={t("ticket.list.delete")}>
              <DeleteOutlined
                className="!text-[#9da3af] !text-sm cursor-pointer hover:!text-[#ff4d4f]"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  confirm(record);
                }}
              />
            </Tooltip>
          </Col>
        );
      },
    },
  ];

  const confirm = (record: any) => {
    Modal.confirm({
      title: t("ticket.list.warning"),
      icon: null,
      content: t("ticket.list.deleteModalDesc"),
      okText: t("ticket.list.delete"),
      cancelText: t("ticket.list.cancel"),
      onOk: async () => {
        try {
          await deleteTicket(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getTicketListFilter,
            exact: false,
          });
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetTicketFilter({ filter: newFilter }));
  };

  const getTicketInfoes = async (ticketId: string) => {
    try {
      const response = await getTicketForDetail(ticketId);

      dispatch(hanldleSetTicketDetails({ data: response?.Value }));
    } catch (error) {
      showErrorCatching(error, null, false, t);
    }
  };

  return (
    <>
      {topTicketId ? (
        <>
          {tickets?.data?.Value?.length > 0 && (
            <Row gutter={[0, 10]}>
              <Col xs={24}>
                <Text className="!font-bold !text-xs">
                  {t("ticket.list.subTickets")}
                </Text>
              </Col>
              <Col xs={24}>
                <TableWithColumnVisibility2
                  columns={columns}
                  dataSource={tickets?.data?.Value}
                  scroll={{ x: 700 }}
                  loading={tickets.isLoading || tickets.isFetching}
                  onRow={(record) => {
                    return {
                      onClick: async (event) => {
                        await dispatch(
                          hanldleSetTicketDetails({ data: record })
                        );
                        queryClient.resetQueries({
                          queryKey: endPoints.getTicketDetail,
                          exact: false,
                        });
                        setIsShowEditDrawer(true);
                      },
                    };
                  }}
                  pagination={{
                    position: ["bottomRight"],
                    className: "!px-0",
                    onChange: handleChangePagination,
                    total: tickets.data?.FilteredCount || 0,
                    current: tickets.data?.PageNumber,
                    pageSize: tickets.data?.PageSize,
                    showLessItems: true,
                    size: "small",
                    showSizeChanger: true,
                    locale: { items_per_page: "" },
                    showTotal: (e) => `${e}`,
                  }}
                  rowKey={"Id"}
                />
              </Col>
            </Row>
          )}
        </>
      ) : (
        <>
          <TableWithColumnVisibility1
            columns={columns}
            dataSource={tickets?.data?.Value}
            scroll={{ x: 700 }}
            loading={tickets.isLoading || tickets.isFetching}
            onRow={(record) => {
              return {
                onClick: async (event) => {
                  await dispatch(hanldleSetTicketDetails({ data: record }));
                  queryClient.resetQueries({
                    queryKey: endPoints.getTicketDetail,
                    exact: false,
                  });
                  setIsShowEditDrawer(true);
                },
              };
            }}
            pagination={{
              position: ["bottomRight"],
              className: "!px-0",
              onChange: handleChangePagination,
              total: tickets.data?.FilteredCount || 0,
              current: tickets.data?.PageNumber,
              pageSize: tickets.data?.PageSize,
              showLessItems: true,
              size: "small",
              showSizeChanger: true,
              locale: { items_per_page: "" },
              showTotal: (e) => `${e}`,
            }}
            rowKey={"Id"}
          />
        </>
      )}

      {/* Edit Drawer */}
      <Drawer
        width={"100%"}
        bodyStyle={{ padding: 0 }} // Padding'i tamamen kaldırır
        title={
          <div className="flex items-center justify-between w-full pr-8">
            <span>
              {ticketDetails?.Id
                ? t("ticket.list.editTicket") + " (" + ticketDetails?.Code + ")"
                : t("ticket.list.addTicket")}
            </span>
            <MazakaButton
              htmlType="button"
              status="save"
              icon={<SaveOutlined />}
              size="small"
              onClick={() => {
             
                triggerTicketFormSubmit();
              }}
            >
              {t("ticket.list.save")}
            </MazakaButton>
          </div>
        }
        open={isShowEditDrawer}
        onClose={() => {
          if (ticketDetails?.TopTicketId) {
            getTicketInfoes(ticketDetails?.TopTicketId);
          } else {
            dispatch(handleResetAllFieldsTicket());
          }

          setIsShowEditDrawer(false);
          searchParams.delete("TopTicketId");
          setSearchParams(searchParams);
          if (searchParams.get("customerId")) {
            searchParams.delete("customerId");
            setSearchParams(searchParams);
          }
        }}
        className="drawer-body"
      >
        <AddOrUpdateIndex
          onSubmitSuccess={() => {
            if (!ticketDetails) {
              dispatch(handleResetAllFieldsTicket());
            }
          }}
        />
      </Drawer>
    </>
  );
};

export default ListItems;
