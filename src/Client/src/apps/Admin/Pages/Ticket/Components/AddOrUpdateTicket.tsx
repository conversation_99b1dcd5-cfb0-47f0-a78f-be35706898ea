// AddOrUpdateTicket.tsx - Loading States ile Geliştirilmiş
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import useMazakaForm from "@/hooks/useMazakaForm";
import {
  Card,
  Col,
  Divider,
  Form,
  Row,
  Space,
  Tabs,
  Spin,
  Checkbox,
  Tooltip,
  Drawer,
} from "antd";
import { useEffect, useState, useCallback, useRef } from "react";
import { useQueryClient } from "react-query";
import endPoints from "../EndPoints";
import {
  createTicket,
  postTicketTransitionsExecute,
  updateTicketWithPut,
} from "../Services";
import GeneralSubjectTicket from "@/apps/Common/GeneralSubjectTicket";
import GeneralCustomer from "@/apps/Common/GeneralCustomer";
import GeneralDepartments from "@/apps/Common/Departments/GeneralDepartments";
import {
  MessageOutlined,
  HistoryOutlined,
  FileTextOutlined,
  LoadingOutlined,
  PlusOutlined,
  SaveOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { MazakaDatePicker } from "@/apps/Common/MazakaDatePicker";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import GeneralUsers from "@/apps/Common/GenerralUsers";
import GeneralNotificationWays from "@/apps/Common/GeneralNotificationWays";
import { hanldleSetTicketDetails } from "../ClientSideStates";
import { Typography } from "antd";
import GeneralStatusTicket from "@/apps/Common/GeneralStatusTicket";
import { useGetTicketDetail } from "../ServerSideStates";
import GeneralTicketList from "@/apps/Common/GeneralAllTickets";
import TicketCommentIndex from "./Comments/TicketCommentIndex";
import TicketHistoryIndex from "./TicketHistory/TicketHistoryIndex";
import Title from "./Title";
import ReusableFileManager, {
  FileItem,
} from "@/apps/Common/ReusableFileManager";
import SimpleMapComponent from "@/apps/Common/GoogleMapPin";
import { useSearchParams } from "react-router-dom";
import { FormContext } from "@/hooks/useSharedForm";
import WrapperTicketForm from "./WrapperTicketForm";
import GeneralTicketType from "@/apps/Common/GeneralTicketType";
import SubTickets from "./SubTickets";
import GeneralPlaceSearchInput from "@/apps/Common/GeneralPlaceSearchInput";
import GeneralTicketTags from "./Tags";
import AddOrUpdateCustomerIndex from "../../Customers/Components/AddOrUpdate/AddOrUpdateCustomerIndex";
import { getChatMessageDetails, sendMessage } from "@/apps/Chat/Services";
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";

const { Text } = Typography;

// Address Info interface
interface AddressAttributeData {
  latitude?: number;
  longitude?: number;
  address?: string;
  country?: string;
  state?: string;
  city?: string;
  province?: string;
}

// Initial state değerleri
const INITIAL_ADDRESS_STATE: AddressAttributeData = {
  latitude: undefined,
  longitude: undefined,
  address: "",
  country: undefined,
  state: undefined,
  city: undefined,
  province: undefined,
};

const INITIAL_FORM_VALUES = {
  NotificationWay: 4,
  Priority: 2,
  Country: "Türkiye",
  State: "Kırıkkale",
  City: "",
  Province: "",
  Detail: "",
  selectAddressType: "selectMap",
};

interface AddOrUpdateTicketProps {
  onSubmitSuccess?: () => void;
}

const AddOrUpdateTicket: React.FC<AddOrUpdateTicketProps> = ({
  onSubmitSuccess,
}) => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { filter, pageType, ticketDetails } = useSelector(
    (state: RootState) => state.ticket
  );
   const geocoderRef = useRef<google.maps.Geocoder | null>(null);
  const [chatFiles, setChatFiles] = useState<any[]>([]);
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const dispatch = useDispatch();
  const [isShowCustomerDrawer, setIsShowCustomerDrawer] = useState(false);
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const [type, setType] = useState<string | null>(null);
  const [inputValue, setInputValue] = useState("");
  const [selectAddressType, setSelectAddressType] = useState<string | null>(
    "selectMap"
  );
  const [addressInfoes, setAddressInfoes] = useState<{
    country: string;
    state: string;
    city: string;
  }>({ country: "", state: "", city: "" });

  // Get URL parameters:
  const ticketChatId = searchParams.get("ticketChatId");
  const customerId = searchParams.get("customerId");

  // State'ler
  const [fileList, setFileList] = useState<FileItem[]>([]);

  const [marker, setMarker] = useState<google.maps.LatLngLiteral | null>(null);

  const [addressInfo, setAddressInfo] = useState<AddressAttributeData>(
    INITIAL_ADDRESS_STATE
  );
  const [isFormDirty, setIsFormDirty] = useState(false); // Form değişiklik takibi
  const [showMapComponent, setShowMapComponent] = useState(false); // Harita görünürlük kontrolü
  const [showManualAddressInputs, setShowManualAddressInputs] = useState(false); // Manuel adres input'ları görünürlüğü

  // Loading states
  const [isStatusChanging, setIsStatusChanging] = useState(false);
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);
  const [isSearchParamsLoading, setIsSearchParamsLoading] = useState(false);

  const serverTicketDetails = useGetTicketDetail(ticketDetails?.Id);

  // Ticket ID değişikliği takibi için state
  const [previousTicketId, setPreviousTicketId] = useState<string | null>(null);

  // Dosya uzantısından mime type çıkarma - Memoized
  const getMimeTypeFromFileName = useCallback((fileName: string): string => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    const mimeTypes: Record<string, string> = {
      jpg: "image/jpeg",
      jpeg: "image/jpeg",
      png: "image/png",
      gif: "image/gif",
      webp: "image/webp",
      svg: "image/svg+xml",
      pdf: "application/pdf",
      doc: "application/msword",
      docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      xls: "application/vnd.ms-excel",
      xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ppt: "application/vnd.ms-powerpoint",
      pptx: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      txt: "text/plain",
      zip: "application/zip",
      rar: "application/x-rar-compressed",
      mp4: "video/mp4",
      mp3: "audio/mpeg",
      wav: "audio/wav",
    };
    return mimeTypes[extension || ""] || "application/octet-stream";
  }, []);

  // Tüm state'leri sıfırlama fonksiyonu
  const resetAllStates = useCallback(() => {
    setFileList([]);
    setMarker(null);
    setAddressInfo(INITIAL_ADDRESS_STATE);
    setIsFormDirty(false);
    setShowMapComponent(false);
    setShowManualAddressInputs(false); // Manuel adres input'larını kapat
    setIsStatusChanging(false);
    setIsFormSubmitting(false);
    setIsSearchParamsLoading(false);
    form.resetFields();
    form.setFieldsValue(INITIAL_FORM_VALUES);
  }, [form]);

  // Ticket ID değiştiğinde sıfırlama (farklı ticket'a geçiş)
  useEffect(() => {
    const currentTicketId = ticketDetails?.Id || null;

    // Eğer ticket ID değiştiyse (farklı ticket'a geçiş)
    if (previousTicketId !== null && previousTicketId !== currentTicketId) {
      resetAllStates();
    }

    setPreviousTicketId(currentTicketId);
  }, [
    ticketDetails?.Id,
    resetAllStates,
    previousTicketId,
    setPreviousTicketId,
  ]);

  // Server ticket details effect
  useEffect(() => {
    if (serverTicketDetails.data?.Value) {
      dispatch(
        hanldleSetTicketDetails({ data: serverTicketDetails.data.Value })
      );
    }
  }, [serverTicketDetails.data?.Value, dispatch]);

  // CustomerId URL parametresini form'a set et veya temizle
  useEffect(() => {
    if (!ticketDetails?.Id) {
      // Sadece yeni ticket oluştururken
      if (customerId) {
        // Loading başlat
        setIsSearchParamsLoading(true);

        // Küçük bir delay ile form'un hazır olmasını bekle
        const timer = setTimeout(() => {
          form.setFieldsValue({ CustomerId: customerId });
          setIsFormDirty(true);
          setIsSearchParamsLoading(false); // Loading bitir
        }, 300); // Biraz daha uzun delay - customer bilgilerinin yüklenmesi için

        return () => {
          clearTimeout(timer);
          setIsSearchParamsLoading(false);
        };
      } else {
        // CustomerId yoksa temizle
        form.setFieldsValue({ CustomerId: null });
        setIsFormDirty(true);
        setIsSearchParamsLoading(false);
      }
    }
  }, [customerId, ticketDetails?.Id, form]);

  // SearchParams değişikliklerini takip et - drawer kapandığında form'u temizle
  useEffect(() => {
    const currentCustomerId = searchParams.get("customerId");
    const currentTicketId = searchParams.get("ticketId");

    // Eğer hem customerId hem ticketId yoksa (drawer kapandı), form'u temizle
    if (!currentCustomerId && !currentTicketId && !ticketDetails?.Id) {
      setIsSearchParamsLoading(false); // Loading'i kapat
      resetAllStates();
    }
  }, [searchParams, ticketDetails?.Id, resetAllStates]);

  // AttributeData'dan sadece koordinat bilgilerini yükleme (address bilgileri artık direkt alanlarda)
  const loadAddressFromAttributeData = useCallback((attributeData: any) => {
    if (
      !attributeData ||
      (!attributeData.latitude && !attributeData.longitude)
    ) {
      return;
    }

    const addressData: AddressAttributeData = {
      latitude: attributeData.latitude
        ? parseFloat(attributeData.latitude)
        : undefined,
      longitude: attributeData.longitude
        ? parseFloat(attributeData.longitude)
        : undefined,
      address: "", // Address bilgileri artık direkt entity alanlarından gelir
      country: undefined,
      state: undefined,
      city: undefined,
      province: undefined,
    };

    setAddressInfo(addressData);

    if (addressData.latitude && addressData.longitude) {
      setMarker({
        lat: addressData.latitude,
        lng: addressData.longitude,
      });
      // Koordinat varsa harita component'ini otomatik aç
      setShowMapComponent(true);
    }
  }, []);

  // AttributeData'ya sadece koordinat bilgilerini kaydetme (address bilgileri artık direkt alanlarda)
  const prepareAttributeData = useCallback(
    (currentAttributeData: any = {}) => {
      const attributeData = { ...currentAttributeData };

      // Sadece koordinat bilgilerini AttributeData'da tut
      if (
        addressInfo.latitude !== undefined &&
        addressInfo.longitude !== undefined
      ) {
        attributeData.latitude = addressInfo.latitude.toString();
        attributeData.longitude = addressInfo.longitude.toString();
      } else {
        // Koordinat yoksa sil
        delete attributeData.latitude;
        delete attributeData.longitude;
      }

      // Eski address bilgilerini AttributeData'dan temizle (artık direkt alanlarda)
      delete attributeData.address;
      delete attributeData.country;
      delete attributeData.state;
      delete attributeData.city;
      delete attributeData.province;

      return attributeData;
    },
    [addressInfo]
  );

  // Address alanlarını temizleme
  const clearAddressData = useCallback(() => {
    setAddressInfo(INITIAL_ADDRESS_STATE);
    setMarker(null);
    setShowMapComponent(false);
    form.setFieldsValue({ Address: "" });
    setIsFormDirty(true);
  }, [form]);

  // Tabs - Memoized
  const tabItems = ticketDetails
    ? [
        {
          key: "1",
          label: (
            <Space size="small">
              <MessageOutlined style={{ fontSize: "16px" }} />
              <span>Aktiviteler</span>
            </Space>
          ),
          children: (
            <Card size="small" style={{ minHeight: "400px" }}>
              <TicketCommentIndex />
            </Card>
          ),
        },
        {
          key: "2",
          label: (
            <Space size="small">
              <HistoryOutlined style={{ fontSize: "16px" }} />
              <span>Geçmiş</span>
            </Space>
          ),
          children: (
            <Card size="small" style={{ minHeight: "400px" }}>
              <TicketHistoryIndex />
            </Card>
          ),
        },
        {
          key: "3",
          label: (
            <Space size="small">
              <FileTextOutlined style={{ fontSize: "16px" }} />
              <span>Sistem Logları</span>
            </Space>
          ),
          children: (
            <Card size="small" style={{ minHeight: "400px" }}>
              <div
                style={{
                  padding: "20px",
                  textAlign: "center",
                  color: "#8c8c8c",
                }}
              >
                <FileTextOutlined
                  style={{ fontSize: "48px", marginBottom: "16px" }}
                />
                <Title level={4} type="secondary">
                  Sistem Logları
                </Title>
                <p>Sistem logları burada gösterilecek...</p>
              </div>
            </Card>
          ),
        },
        // {
        //   key: "4",
        //   label: (
        //     <Space size="small">
        //       <EyeOutlined style={{ fontSize: "16px" }} />
        //       <span>Takip Edenler</span>
        //       {ticketDetails?.Watchlist?.length > 0 && (
        //         <Badge
        //           count={ticketDetails.Watchlist.length}
        //           size="small"
        //           style={{ backgroundColor: "#52c41a" }}
        //         />
        //       )}
        //     </Space>
        //   ),
        //   children: (
        //     <Card size="small" style={{ minHeight: "400px" }}>
        //       <TicketWatchersIndex />
        //     </Card>
        //   ),
        // },
      ]
    : [];

  // Status değişikliği - Loading state ile
  const handlePostTransitionStatus = useCallback(
    async (transitionId: string) => {
      setIsStatusChanging(true);
      try {
        await postTicketTransitionsExecute(ticketDetails.Id, transitionId);
        queryClient.resetQueries({
          queryKey: endPoints.getTicketDetail,
          exact: false,
        });
        queryClient.resetQueries({
          queryKey: endPoints.getTicketForStatus,
          exact: false,
        });
        openNotificationWithIcon("success", "Durum başarıyla güncellendi");
      } catch (error) {
        showErrorCatching(error, null, false, t);
      } finally {
        setIsStatusChanging(false);
      }
    },
    [ticketDetails?.Id, queryClient, t]
  );

  const priorityOptions = [
    { label: t("ticket.list.low"), value: 1 },
    { label: t("ticket.list.medium"), value: 2 },
    { label: t("ticket.list.high"), value: 3 },
    { label: t("ticket.list.critical"), value: 4 },
  ];

  // Ticket details yüklendiğinde form populate et
  useEffect(() => {
    if (ticketDetails) {
      const data = { ...ticketDetails };
      data["EndDate"] = dayjs(data["EndDate"]);
      data["StatusName"] = ticketDetails?.StatusName;
      data["DepartmentIds"] = ticketDetails?.Departments?.map(
        (item: any) => item?.DepartmentId
      );
      // CustomerId'yi sadece mevcut ticket'ta override etme, yeni ticket'ta URL'den gelsin
      if (customerId && ticketDetails?.Id) {
        data["CustomerId"] = searchParams.get("customerId");
      }
      data["Tags"] = ticketDetails.Tags || [];
      if (data?.AttributeData?.latitude && data?.AttributeData?.longitude) {
        setSelectAddressType("selectMap");
        form.setFieldValue("selectAddressType", "selectMap");
        setMarker({
          lat: data?.AttributeData?.latitude,
          lng: data?.AttributeData?.longitude,
        });
      } else if (
        !data?.AttributeData?.latitude &&
        !data?.AttributeData?.longitude
      ) {
        setSelectAddressType("addManually");
        form.setFieldValue("selectAddressType", "addManually");
      }

      form.setFieldsValue(data);

      // Dosyaları yükle
      if (ticketDetails.TicketFiles) {
        const normalizedFiles: FileItem[] = ticketDetails.TicketFiles.map(
          (file: any) => ({
            FileId: file.FileId || file.Id,
            FileName: file.FileName,
            FilePath: file.FilePath,
            MimeType: file.MimeType || getMimeTypeFromFileName(file.FileName),
            FileSizeInBytes: file.FileSizeInBytes,
          })
        );
        setFileList(normalizedFiles);
      }

      // Address alanlarını direkt form'a set et (artık JSON'da değil direkt alanlarda)
      if (
        ticketDetails.Country ||
        ticketDetails.State ||
        ticketDetails.City ||
        ticketDetails.Province
      ) {
        form.setFieldsValue({
          Country: ticketDetails.Country || "",
          State: ticketDetails.State || "",
          City: ticketDetails.City || "",
          Province: ticketDetails.Province || "",
          Detail: ticketDetails.Detail || "",
          StatusType: ticketDetails.StatusType,
        });

        setInputValue(ticketDetails.Detail || "");

        // Address info state'ini de güncelle (harita için)
        setAddressInfo({
          latitude: undefined,
          longitude: undefined,
          address: ticketDetails.Detail || "",
          country: ticketDetails.Country,
          state: ticketDetails.State,
          city: ticketDetails.City,
          province: ticketDetails.Province,
        });
      }

      // Eski AttributeData'dan address bilgilerini yükle (backward compatibility)
      if (ticketDetails.AttributeData) {
        try {
          const attributeData =
            typeof ticketDetails.AttributeData === "string"
              ? JSON.parse(ticketDetails.AttributeData)
              : ticketDetails.AttributeData;
          loadAddressFromAttributeData(attributeData);
        } catch (error) {
          console.error("AttributeData parse edilemedi:", error);
        }
      }

      setIsFormDirty(false); // Form yüklendiğinde dirty flag'i sıfırla
    } else {
      // Yeni ticket oluşturma modunda state'leri sıfırla
      resetAllStates();
    }
  }, [
    ticketDetails,
    getMimeTypeFromFileName,
    loadAddressFromAttributeData,
    resetAllStates,
    form,
  ]);

  // Form değişiklik takibi
  const handleFormChange = useCallback(() => {
    setIsFormDirty(true);
  }, []);

  // File list değişiklik takibi
  const handleFileListChange = useCallback((newFileList: FileItem[]) => {
    setFileList(newFileList);
    setIsFormDirty(true);
  }, []);

  const handleOnFinish = useCallback(async () => {
    setIsFormSubmitting(true);
    try {
      const formValues = form.getFieldsValue();
      formValues["TicketFiles"] = fileList;

      // Address bilgilerini direkt form'dan al (artık JSON'da değil)
      // Manuel input'lar açıksa form'dan, kapalıysa arka plandaki değerlerden al
      formValues["Detail"] = inputValue;
      if (showManualAddressInputs) {
        // Manuel input'lar açık - form'dan al
        formValues["Country"] = formValues["Country"] || "";
        formValues["State"] = formValues["State"] || "";
        formValues["City"] = formValues["City"] || "";
        formValues["Province"] = formValues["Province"] || "";
      } else {
        // Manuel input'lar kapalı - arka plandaki değerlerden al (Google Maps'ten gelen)
        formValues["Country"] =
          addressInfo.country || formValues["Country"] || "";
        formValues["State"] = addressInfo.state || formValues["State"] || "";
        formValues["City"] = addressInfo.city || formValues["City"] || "";
        formValues["Province"] =
          addressInfo.province || formValues["Province"] || "";
      }

      // AttributeData'yı hazırla (koordinatlar için)
      const currentAttributeData = ticketDetails?.AttributeData
        ? typeof ticketDetails.AttributeData === "string"
          ? JSON.parse(ticketDetails.AttributeData)
          : ticketDetails.AttributeData
        : {};

      // Koordinat bilgilerini AttributeData'da tut, address bilgilerini temizle
      const preparedAttributeData = prepareAttributeData(currentAttributeData);
      formValues["AttributeData"] = preparedAttributeData;
      delete formValues["selectAddressType"];
      let response;
      if (ticketDetails) {
        const data = { ...ticketDetails };
        delete data["CustomerName"];
        if (!formValues["CustomerId"]) {
          formValues["CustomerId"] = null;
        }

        formValues["ChatId"] = ticketChatId;
        await updateTicketWithPut({ ...data, ...formValues });
      } else {
        if (ticketChatId) {
          formValues["ChatId"] = ticketChatId;
        }
        if (formValues?.Tags?.length > 0) {
          formValues["Tags"] = formValues["Tags"];
        } else {
          formValues["Tags"] = [];
        }
        response = await createTicket(formValues);
        dispatch(hanldleSetTicketDetails({ data: { Id: response?.Value?.Id } }));
        const message = {
          ChatId: ticketChatId,
          Direction: 2,
          SenderId: userInfoes?.Id,
          ContentType: 1,
          Content: `${response?.Value?.Code} nolu ticketiniz oluşturuldu`,
        };
  
        await sendMessage(message);
      }

      openNotificationWithIcon("success", t("form.transactionSuccessful"));

     

      // Sadece yeni ticket oluşturulduğunda state'leri sıfırla
      if (!ticketDetails) {
        resetAllStates();
      } else {
        setIsFormDirty(false); // Update işleminde sadece dirty flag'i sıfırla
      }

      queryClient.resetQueries({
        queryKey: endPoints.getTicketListFilter,
        exact: false,
      });
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    } finally {
      setIsFormSubmitting(false);
    }
  }, [
    form,
    fileList,
    ticketDetails,
    prepareAttributeData,
    pageType,
    filter,
    dispatch,
    t,
    resetAllStates,
    queryClient,
    mazakaForm,
    ticketChatId,
  ]);

  useEffect(() => {
    form.setFieldsValue({
      Country: addressInfoes?.country,
      State: addressInfoes?.state,
      City: addressInfoes?.city,
    });
  }, [addressInfoes]);

  useEffect(() => {
    if (searchParams.get("TopTicketId")) {
      form.setFieldValue("TopTicketId", searchParams.get("TopTicketId"));
    }
  }, [searchParams]);

  const handleGetChatFiles = async (chatId: string) => {
    try {
      const response = await getChatMessageDetails(chatId);
      const messages = response?.Value?.Messages || [];
      const filersMessages = messages?.filter(
        (item: any) =>
          item?.ContentType !== "Text" && item?.ContentType !== "Location"&&item?.ContentType !== "Sticker"
      );

      const attachments = filersMessages?.map((item: any) => {
        return {
          FilePath: `${setBackEndUrl()}/Uploads/${
            item?.Attachments[0]?.FilePath
          }`,
          type: item.ContentType,
          FileId: item?.Attachments[0]?.FileId,
          FileName: item?.Attachments[0]?.FileName,
          
        };
      });
      setFileList([...fileList,...attachments])
    } catch (error) {
      showErrorCatching(error, null, false, t);
    }
  };

  useEffect(()=>{
 
    if(searchParams.get("ticketChatId"))
    {
      handleGetChatFiles(searchParams.get("ticketChatId")||"")
    }
    if(searchParams.get("externalLat")&&searchParams.get("externalLng"))
    {
  
      let lat =Number( searchParams.get("externalLat"))
      let lng =Number( searchParams.get("externalLng"))
      setMarker({lat,lng})
      
  
      if (geocoderRef.current) {
        const latLng = new google.maps.LatLng(lat, lng);
    
        geocoderRef.current.geocode({ location: latLng }, (results, status) => {
          
        
          setInputValue(results?.[0]?.formatted_address||"")
         
        });
      }
    }
  },[searchParams,geocoderRef.current])


  

  return (
    <>
      <FormContext.Provider value={form}>
        <WrapperTicketForm onSubmitSuccess={onSubmitSuccess}>
          <Spin
            spinning={
              isFormSubmitting || isStatusChanging || isSearchParamsLoading
            }
            tip={
              isFormSubmitting
                ? "Kayıt ediliyor..."
                : isStatusChanging
                ? "Durum güncelleniyor..."
                : isSearchParamsLoading
                ? "Veriler yükleniyor..."
                : ""
            }
            size="large"
            style={{ padding: "0px 16px 8px 16px" }}
          >
            <MazakaForm
              id="ticket-form"
              className=" ticket-form !rounded-none !border-none !p-0 !pt-0 !mt-0"
              form={form}
              onFinish={handleOnFinish}
              onFieldsChange={handleFormChange} // Form değişiklik takibi
              submitButtonVisible={false}
              initialValues={INITIAL_FORM_VALUES}
              style={{ padding: "0px 16px 16px 16px", margin: "0px" }}
            >
              <Row
                gutter={[16, 8]}
                className="!rounded-none border-none !p-0 !pt-0 !mt-0"
              >
                {/* Sol Taraf - Ticket Detayları */}
                <Col xs={24} lg={16} className="border-0">
                  <Space
                    direction="vertical"
                    size="small"
                    className="rounded-none border-none"
                    style={{ width: "100%" }}
                  >
                    <Card
                      size="small"
                      className="!rounded-none !border-none !mt-0 !pt-0"
                      headStyle={{
                        backgroundColor: "#fafafa",
                        fontWeight: 600,
                      }}
                      style={{ marginTop: "0px", paddingTop: "0px" }}
                    >
                      <Space
                        direction="vertical"
                        size="middle"
                        style={{ width: "100%" }}
                      >
                        <Row gutter={[8, 8]}>
                          <Col xs={12}>
                            <GeneralSubjectTicket
                              label={t("ticket.list.subject")}
                              placeholder={t("ticket.list.subject")}
                              xs={24}
                              name="SubjectId"
                              className="d-flex !flex-row ps-1"
                              rules={[{ required: true, message: "" }]}
                              labelCol={{ span: 4 }}
                              wrapperCol={{ span: 24 }}
                              onChange={(value: string, obj: any) => {
                                const sla: number = obj?.sla || 0;
                                const now = dayjs();

                                const resultTime = now.add(sla, "minute");
                                form.setFieldValue("EndDate", resultTime);
                              }}
                            />
                          </Col>

                          <Col xs={12}>
                            <MazakaInput
                              label={t("ticket.list.title")}
                              placeholder={t("ticket.list.title")}
                              xs={24}
                              name="Title"
                              labelCol={{ span: 4 }}
                              wrapperCol={{ span: 24 }}
                              rules={[{ required: true, message: "" }]}
                            />
                          </Col>
                        </Row>

                        <MazakaTextArea
                          xs={24}
                          name="Description"
                          className=""
                          labelCol={{ span: 4 }}
                          wrapperCol={{ span: 24 }}
                          label={t("ticket.list.description")}
                          placeholder={t("ticket.list.description")}
                          rows={10}
                        />

                        <Col xs={24}>
                          <Row gutter={[10, 10]}>
                            <MazakaSelect
                              label="Adres"
                              name="selectAddressType"
                              xs={24}
                              md={12}
                              xl={5}
                              options={[
                                {
                                  label: "Manuel Adres Ekle",
                                  value: "addManually",
                                },
                                {
                                  label: "Haritadan Konum Belirle",
                                  value: "selectMap",
                                },
                              ]}
                              onChange={(value: string) => {
                                setSelectAddressType(value);
                              }}
                            />

                            {(() => {
                              if (selectAddressType === "addManually") {
                                return (
                                  <Col xs={19}>
                                    <Row gutter={[10, 10]}>
                                      <MazakaInput
                                        name={"Country"}
                                        xs={24}
                                        md={12}
                                        xl={6}
                                        label={t("customers.add.country")}
                                        placeholder={t("customers.add.country")}
                                        onChange={(e: any) => {
                                          const value = e.target.value;

                                          setAddressInfoes({
                                            ...addressInfoes,
                                            country: value || "",
                                          });
                                        }}
                                      />
                                      <MazakaInput
                                        name={"State"}
                                        xs={24}
                                        md={12}
                                        xl={6}
                                        label={t("customers.add.state")}
                                        placeholder={t("customers.add.state")}
                                        onChange={(e: any) => {
                                          const value = e.target.value;
                                          setAddressInfoes({
                                            ...addressInfoes,
                                            state: value || "",
                                          });
                                        }}
                                      />
                                      <MazakaInput
                                        name={"City"}
                                        xs={24}
                                        md={12}
                                        xl={6}
                                        label={t("customers.add.city")}
                                        placeholder={t("customers.add.city")}
                                        onChange={(e: any) => {
                                          const value = e.target.value;
                                          setAddressInfoes({
                                            ...addressInfoes,
                                            city: value || "",
                                          });
                                        }}
                                      />
                                      <MazakaInput
                                        name={"Detail"}
                                        xs={24}
                                        md={12}
                                        xl={6}
                                        label={t("customers.add.address")}
                                        placeholder={t("customers.add.address")}
                                      />
                                    </Row>
                                  </Col>
                                );
                              } else if (selectAddressType === "selectMap")
                                return (
                                  <>
                                    <GeneralPlaceSearchInput
                                      xs={24}
                                      xl={19}
                                      setAddressInfoes={setAddressInfoes}
                                      form={form}
                                      inputValue={inputValue}
                                      setInputValue={setInputValue}
                                      fullAddressPropertyName="Detail"
                                      setMarker={setMarker}
                                      classNames="!mt-7"
                                    />

                                    <SimpleMapComponent
                                      marker={marker}
                                      geocoderRef={geocoderRef }
                                      address={addressInfo.address}
                                      setPlaceSearchInputValue={setInputValue}
                                      placeSearchInputValue={inputValue}
                                      onAddressChange={(address) => {
                                        setAddressInfo((prev) => ({
                                          ...prev,
                                          address,
                                        }));
                                        form.setFieldValue("Detail", address); // Detail alanına set et
                                        setIsFormDirty(true);
                                      }}
                                      onMarkerChange={(
                                        coords,
                                        address,
                                        addressComponents
                                      ) => {
                                        setMarker(coords);

                                        // AddressInfo'ya koordinat ve address bilgilerini set et
                                        const newAddressInfo = {
                                          latitude: coords?.lat,
                                          longitude: coords?.lng,
                                          address: address,
                                          country: addressComponents?.country,
                                          state: addressComponents?.state,
                                          city: addressComponents?.city,
                                          province: addressComponents?.province,
                                        };
                                        setAddressInfo(newAddressInfo);

                                        // Address bilgilerini direkt form alanlarına set et
                                        form.setFieldsValue({
                                          Detail: address,
                                          Country:
                                            addressComponents?.country || "",
                                          State: addressComponents?.state || "",
                                          City: addressComponents?.city || "",
                                          Province:
                                            addressComponents?.province || "",
                                        });
                                        setIsFormDirty(true);
                                      }}
                                      visible={true}
                                      showInlineMap={true} // Her zaman göster çünkü zaten showMapComponent ile kontrol ediliyor
                                      compactMode={true}
                                      clearAddressData={clearAddressData}
                                    />
                                  </>
                                );
                            })()}
                          </Row>
                        </Col>

                        {/* <Col xs={24}>
                          <Row gutter={[20, 0]}>
                            <GeneralPlaceSearchInput
                              xs={24}
                              xl={18}
                              setAddressInfoes={setAddressInfoes}
                              form={form}
                              inputValue={inputValue}
                              setInputValue={setInputValue}
                              fullAddressPropertyName="Detail"
                              setMarker={setMarker}
                            />
                            <Col xs={24} xl={6}>
                              <Form.Item>
                                <Checkbox
                                  checked={isShowMapComponent}
                                  onChange={() => {
                                    setIsShowMapComponent(!isShowMapComponent);
                                  }}
                                >
                                  Haritadan Konum Belirle
                                </Checkbox>
                              </Form.Item>
                            </Col>
                            {isShowMapComponent && (
                              <Col
                                xs={24}
                                className=" justify-end items-center"
                              >
                                <SimpleMapComponent
                                  marker={marker}
                                  address={addressInfo.address}
                                  setPlaceSearchInputValue={setInputValue}
                                  placeSearchInputValue={inputValue}
                                  onAddressChange={(address) => {
                                    setAddressInfo((prev) => ({
                                      ...prev,
                                      address,
                                    }));
                                    form.setFieldValue("Detail", address); // Detail alanına set et
                                    setIsFormDirty(true);
                                  }}
                                  onMarkerChange={(
                                    coords,
                                    address,
                                    addressComponents
                                  ) => {
                                    setMarker(coords);

                                    // AddressInfo'ya koordinat ve address bilgilerini set et
                                    const newAddressInfo = {
                                      latitude: coords?.lat,
                                      longitude: coords?.lng,
                                      address: address,
                                      country: addressComponents?.country,
                                      state: addressComponents?.state,
                                      city: addressComponents?.city,
                                      province: addressComponents?.province,
                                    };
                                    setAddressInfo(newAddressInfo);

                                    // Address bilgilerini direkt form alanlarına set et
                                    form.setFieldsValue({
                                      Detail: address,
                                      Country: addressComponents?.country || "",
                                      State: addressComponents?.state || "",
                                      City: addressComponents?.city || "",
                                      Province:
                                        addressComponents?.province || "",
                                    });
                                    setIsFormDirty(true);
                                  }}
                                  visible={true}
                                  showInlineMap={true} // Her zaman göster çünkü zaten showMapComponent ile kontrol ediliyor
                                  compactMode={true}
                                  clearAddressData={clearAddressData}
                                />
                              </Col>
                            )}
                          </Row>
                        </Col>
                        {isShowManullayAddressConent && (
                          <>
                            <MazakaInput
                              name={"Country"}
                              xs={24}
                              label={t("customers.add.country")}
                              placeholder={t("customers.add.country")}
                              onChange={(e: any) => {
                                const value = e.target.value;

                                setAddressInfoes({
                                  ...addressInfoes,
                                  country: value || "",
                                });
                              }}
                            />
                            <MazakaInput
                              name={"State"}
                              xs={24}
                              label={t("customers.add.state")}
                              placeholder={t("customers.add.state")}
                              onChange={(e: any) => {
                                const value = e.target.value;
                                setAddressInfoes({
                                  ...addressInfoes,
                                  state: value || "",
                                });
                              }}
                            />
                            <MazakaInput
                              name={"City"}
                              xs={24}
                              label={t("customers.add.city")}
                              placeholder={t("customers.add.city")}
                              onChange={(e: any) => {
                                const value = e.target.value;
                                setAddressInfoes({
                                  ...addressInfoes,
                                  city: value || "",
                                });
                              }}
                            />
                          </>
                        )} */}
                      </Space>

                      <Col xs={24}>
                        <SubTickets />
                      </Col>

                      {/* ReusableFileManager Component Kullanımı */}
                      <ReusableFileManager
                        fileList={fileList}
                        onFileListChange={handleFileListChange} // Tracked file change
                        title="Ticket Dosyaları"
                        showCount={true}
                        maxFiles={100}
                        folderPath="Tickets"
                        imageSize={{ width: 100, height: 100 }}
                        cardSize="small"
                        className="mt-0 pt-0"
                        emptyMessage="Ticket dosyası eklemek için + butonuna tıklayın."
                      />

                      {/* Sadece ticketDetails varsa tabs göster */}
                      {ticketDetails && (
                        <Card
                          size="small"
                          className="mt-2 !border-none !rounded-none"
                        >
                          <Tabs
                            defaultActiveKey="1"
                            items={tabItems}
                            type="line"
                            className="ticket-detail-tabs"
                            tabBarStyle={{
                              marginBottom: "5x",
                              borderBottom: "1px solid #f0f0f0",
                            }}
                            tabBarGutter={32}
                            animated={{ inkBar: true, tabPane: true }}
                          />
                        </Card>
                      )}
                    </Card>
                  </Space>
                </Col>

                {/* Sağ Taraf - Form Controls */}
                <Col xs={24} lg={8} className="h-100 ticket-form-attach">
                  <Card
                    size="small"
                    style={{ height: "100%", backgroundColor: "#fafafa" }}
                    className="mt-1 rounded-none"
                    headStyle={{ backgroundColor: "#fafafa", fontWeight: 600 }}
                  >
                    <Space
                      direction="vertical"
                      className="rounded-none p-0 m-0"
                      size="middle"
                      style={{ width: "100%", backgroundColor: "#fafafa" }}
                    >
                      {ticketDetails && (
                        <Row
                          gutter={8}
                          align="bottom"
                          style={{ width: "100%" }}
                        >
                          {ticketDetails && (
                            <Col flex={1}>
                              <GeneralStatusTicket
                                placeholder={t("ticket.list.status")}
                                xs={24}
                                ticketId={ticketDetails?.Id}
                                className="bg-white"
                                name="StatusName"
                                onChange={handlePostTransitionStatus}
                                isLoading={isStatusChanging}
                              />
                            </Col>
                          )}

                          {/* Submit butonu artık drawer başlığında - burayı gizle */}
                          {false && (
                            <Col>
                              <Form.Item className="mb-0 w-100">
                                <MazakaButton
                                  processType={formActions.submitProcessType}
                                  htmlType="submit"
                                  status="save"
                                  className="h-8 max-h-8 w-100"
                                  style={{
                                    height: "32px",
                                    maxHeight: "32px",
                                    width: "100%",
                                  }}
                                  loading={isFormSubmitting}
                                  disabled={isStatusChanging}
                                  icon={
                                    isFormSubmitting ? (
                                      <LoadingOutlined />
                                    ) : undefined
                                  }
                                >
                                  {isFormSubmitting
                                    ? ticketDetails
                                      ? "Güncel..."
                                      : "Oluştur..."
                                    : ticketDetails
                                    ? t("ticket.list.edit")
                                    : t("ticket.list.add")}
                                </MazakaButton>
                              </Form.Item>
                            </Col>
                          )}
                        </Row>
                      )}

                      {ticketDetails && <Divider className="m-1" />}

                      <GeneralUsers
                        label={t("task.list.assignedUser")}
                        placeholder={t("task.list.assignedUser")}
                        name="UserId"
                        xs={24}
                        allowClear={true}
                      />

                      {!ticketDetails && (
                        <GeneralTicketType
                          name="TicketType"
                          label="Ticket Türü"
                          placeholder="Ticket türü seçiniz"
                          xs={24}
                          onChange={(value: number) => {
                            if (value === 1) {
                              setType("ticket");
                            } else if (value === 2) {
                              setType("task");
                            } else {
                              setType(null);
                            }
                          }}
                        />
                      )}

                      <GeneralDepartments
                        label={t("department.departments")}
                        placeholder={t("department.departments")}
                        multiple
                        name="DepartmentIds"
                      />

                      <GeneralTicketList
                        label="Ana Ticket"
                        placeholder="Ana Ticket"
                        ticketId={ticketDetails?.Id}
                        name="TopTicketId"
                        allowClear
                      />
                      <GeneralTicketTags
                        label="Etiketler"
                        placeholder="Etiketler"
                        name="Tags"
                        mode="multiple"
                        initialData={ticketDetails?.Tags || []}
                      />

                      {pageType === "tickets" && (
                        <GeneralCustomer
                          label={t("ticket.list.customer")}
                          placeholder={t("ticket.list.customer")}
                          xs={24}
                          name="CustomerId"
                          rules={[{ required: type === "ticket", message: "" }]}
                          allowClear={type !== "ticket"}
                          onChange={(value: string) => {
                            if (value && searchParams.get("customerId")) {
                              const newParams = new URLSearchParams(
                                searchParams.toString()
                              );
                              newParams.set("customerId", value || "");
                              setSearchParams(newParams);
                            }
                          }}
                          suffixIcon={
                            <Tooltip title={t("customers.addButton")}>
                              <PlusOutlined
                                onClick={() => {
                                  setSearchParams({
                                    addOrUpdateTicket: "true",
                                  });
                                  setIsShowCustomerDrawer(true);
                                }}
                                className="!text-[#0096d1]"
                              />
                            </Tooltip>
                          }
                        />
                      )}

                      {pageType === "tickets" && (
                        <GeneralUsers
                          label={t("ticket.list.watchers")}
                          placeholder={t("ticket.list.watchers")}
                          name="Watchlist"
                          mode="multiple"
                          xs={24}
                        />
                      )}

                      <MazakaSelect
                        label={t("ticket.list.priority")}
                        placeholder={t("ticket.list.priority")}
                        xs={24}
                        name="Priority"
                        options={priorityOptions}
                        rules={[{ required: true, message: "" }]}
                      />

                      <MazakaDatePicker
                        label={t("ticket.list.endDate")}
                        xs={24}
                        name="EndDate"
                        disablePastDates
                      />

                      <GeneralNotificationWays
                        label={t("notificationWay.notificationMethod")}
                        labelCol={{ span: 24 }}
                        wrapperCol={{ span: 24 }}
                        placeholder={t("notificationWay.notificationMethod")}
                        name="NotificationWayId"
                        rules={[{ required: true, message: "" }]}
                        xs={24}
                      />
                    </Space>
                  </Card>
                </Col>
              </Row>
              <>
                {searchParams.get("TopTicketId") && (
                  <MazakaButton
                    className="!fixed top-4 right-9"
                    htmlType="submit"
                    status="save"
                    icon={<SaveOutlined />}
                    size="small"
                  >
                    {t("ticket.list.add")}
                  </MazakaButton>
                )}
              </>
            </MazakaForm>
          </Spin>
        </WrapperTicketForm>
      </FormContext.Provider>

      <Drawer
        open={isShowCustomerDrawer}
        closeIcon={null}
        onClose={() => {
          searchParams.delete("addOrUpdateTicket");
          setSearchParams(searchParams);
          setIsShowCustomerDrawer(false);
        }}
        width={"80%"}
      >
        <AddOrUpdateCustomerIndex
          type="drawer"
          onFinish={() => {
            searchParams.delete("addOrUpdateTicket");
            setSearchParams(searchParams);
            setIsShowCustomerDrawer(false);
          }}
        />
      </Drawer>
    </>
  );
};

export default AddOrUpdateTicket;
