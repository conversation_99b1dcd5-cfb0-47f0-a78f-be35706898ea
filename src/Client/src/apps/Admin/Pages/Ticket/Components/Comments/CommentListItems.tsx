import { RootState } from "@/store/Reducers";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useGetTicketComments } from "../../ServerSideStates";

import { Timeline, Avatar, Space, Row, Col, Pagination, Skeleton } from "antd";
import { UserOutlined } from "@ant-design/icons";

// SlateEditor import
import SlateEditor from '@/apps/Common/Editor/SlateEditor'; // SlateEditor'ınızın path'i

import dayjs from "dayjs";
import PageTitle from "@/apps/Common/PageTitle";
import { useTranslation } from "react-i18next";
import FilePreview from "@/apps/Common/FilePreview";

  // Comment verisini parse etmek için yardımcı fonksiyon
  const parseCommentData = (commentString: string) => {
    try {
      // String ise JSON parse et
      if (typeof commentString === 'string') {
        return JSON.parse(commentString);
      }
      // Zaten obje ise direkt döndür
      return commentString;
    } catch (error) {
      console.error('Comment parse edilemedi:', error);
      // Parse edilemezse string olarak döndür
      return commentString;
    }
  };

const CommentListItems = () => {
  const { ticketDetails } = useSelector((state: RootState) => state.ticket);
  const { t } = useTranslation();
  const [filter, setFilter] = useState({
    PageNumber: 1,
    PageSize: 20,
    TicketId: ticketDetails?.Id,
  });

  useEffect(() => {
    setFilter({ ...filter, TicketId: ticketDetails?.Id, })
  }, [ticketDetails])

  const comments = useGetTicketComments(filter);
  const commentData = comments?.data?.Value ?? [];

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    setFilter(newFilter);
  };

  // Comment'i render etmek için yardımcı fonksiyon
  const renderComment = (commentData: any) => {
    const parsedComment = parseCommentData(commentData);
    
    
    // Eğer parse edilen veri Slate format'ında ise
    if (Array.isArray(parsedComment) && parsedComment.length > 0) {
      // Slate formatını kontrol et
      const isSlateFormat = parsedComment.every(node => 
        node.type && node.children && Array.isArray(node.children)
      );
      
      if (isSlateFormat) {
        return (
          <SlateEditor
            value={parsedComment}
            readOnly={true}
            watchListIds={[ticketDetails.Watchlist]}
            isShowUserSectionMention={false}
            showToolbar={false}
            showMentions={false}
            uploadPcFolderPath="Ticket"
            placeholder=""
            className="!border-0 !shadow-none !bg-transparent !p-0 !m-0"
          />
        );
      }
    }
    
    // Eğer string ise HTML olarak render et
    if (typeof parsedComment === 'string') {
      return (
        <div
          dangerouslySetInnerHTML={{
            __html: parsedComment,
          }}
        />
      );
    }
    
    // Diğer durumlarda string'e çevir
    return <div>{String(parsedComment)}</div>;
  };

  return (
    <Row>
      <Skeleton loading={comments.isLoading || comments.isFetching}>
        {
          comments?.data?.Value?.length > 0 &&
          <>
            <Col xs={24}>
              <PageTitle title={t("ticket.list.comments")} isSubTitle={true} />
            </Col>
            <Col xs={24}>
              <Timeline
                className="!mt-4"
                mode="left"
                items={commentData.map((item: any) => ({
                  children: (
                    <Space align="start">
                      <Avatar
                        className="!w-[25px] !h-[25px]"
                        icon={<UserOutlined />}
                      />
                      <div>
                        <div className="!text-xs !text-gray-500">
                          {dayjs(item.InsertDate).format("DD.MM.YYYY HH:mm")}
                        </div>
                        <div className="!text-xs font-medium">{item.UserName}</div>
                        <div
                          style={{
                            marginTop: 4,
                            lineHeight: '1.4'
                          }}
                        >
                          {renderComment(item.Comment)}
                        </div>

                        {/* FilePreview component kullanımı */}
                        {item.Files && item.Files.length > 0 && (
                          <div style={{ marginTop: '8px' }}>
                            <FilePreview
                              files={item.Files}
                              title="Ekli Dosyalar"
                              showCount={true}
                              imageSize={{ width: 60, height: 60 }}
                              maxWidth="450px"
                              gap="6px"
                              showFileNames={true}
                              showTitle={true}
                            />
                          </div>
                        )}
                      </div>
                    </Space>
                  ),
                }))}
              />
            </Col>
            <Col xs={24} className="!flex justify-end">
              <Pagination
                className="!px-0"
                onChange={handleChangePagination}
                total={comments.data?.FilteredCount || 0}
                current={comments.data?.PageNumber}
                pageSize={comments.data?.PageSize}
                showLessItems
                size="small"
                showSizeChanger
                locale={{ items_per_page: "" }}
                showTotal={(total) => `${total}`}
              />
            </Col>
          </>
        }
      </Skeleton>
    </Row>
  );
};

export default CommentListItems;