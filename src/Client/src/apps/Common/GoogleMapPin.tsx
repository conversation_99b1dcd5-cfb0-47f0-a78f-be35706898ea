import React, { useState, useRef, useEffect, useMemo, useCallback } from "react";
import { Google<PERSON><PERSON>, Marker, useJsApiLoader } from "@react-google-maps/api";
import { Button, Modal } from "antd";
import { DeleteOutlined, EnvironmentOutlined } from "@ant-design/icons";

const GOOGLE_MAP_LIBRARIES = ["places"];
const defaultCenter = { lat: 39.845, lng: 33.5064 };

interface AddressComponents {
  country?: string;
  state?: string;
  city?: string;
  province?: string;
}

interface SimpleMapComponentProps {
  marker?: { lat: number; lng: number } | null;
  address?: string;
  onMarkerChange?: (
    coords: { lat: number; lng: number } | null,
    address: string,
    addressComponents?: AddressComponents
  ) => void;
  onAddressChange?: (address: string) => void;
  visible?: boolean;
  showInlineMap?: boolean;
  compactMode?: boolean;
  clearAddressData: () => void;
  placeSearchInputValue: string;
  setPlaceSearchInputValue: (value: string) => void;
  geocoderRef:any
}

const SimpleMapComponent: React.FC<SimpleMapComponentProps> = ({
  marker,
  address = "",
  onMarkerChange,
  onAddressChange,
  visible = true,
  showInlineMap = false,
  compactMode = false,
  clearAddressData,
  setPlaceSearchInputValue,
  geocoderRef 
}) => {
  const [internalMarker, setInternalMarker] = useState(marker);
  const [internalAddress, setInternalAddress] = useState(address);
  const [showMapPopup, setShowMapPopup] = useState(false);

  const mapRef = useRef<google.maps.Map | null>(null);


  const { isLoaded } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAP_API || "",
    libraries: GOOGLE_MAP_LIBRARIES,
    language: "tr",
  });

  // Harici marker değiştiğinde iç marker'ı güncelle
  useEffect(() => {
    if (marker) {
      setInternalMarker(marker);
    }
  }, [marker]);

  // Marker değişince haritayı merkeze al
  useEffect(() => {
    if (mapRef.current && internalMarker) {
      mapRef.current.setCenter(internalMarker);
    }
  }, [internalMarker]);

  // Geocoder init
  useEffect(() => {
    if (isLoaded && !geocoderRef.current) {
      geocoderRef.current = new google.maps.Geocoder();
    }
  }, [isLoaded]);

  // Modal açıldığında haritayı merkeze al
  useEffect(() => {
    if (showMapPopup && mapRef.current) {
      setTimeout(() => {
        google.maps.event.trigger(mapRef.current, "resize");
        mapRef.current?.setCenter(internalMarker || defaultCenter);
      }, 100);
    }
  }, [showMapPopup, internalMarker]);

  const parseAddressComponents = (components?: google.maps.GeocoderAddressComponent[]): AddressComponents => {
    const result: AddressComponents = {};
    components?.forEach((component) => {
      if (component.types.includes("country")) result.country = component.long_name;
      if (component.types.includes("administrative_area_level_1")) result.state = component.long_name;
      if (component.types.includes("administrative_area_level_2")) result.city = component.long_name;
      if (component.types.includes("sublocality_level_1") || component.types.includes("locality")) result.province = component.long_name;
    });
    return result;
  };

  const handleMapClick = useCallback((event: google.maps.MapMouseEvent) => {
    const latLng = event.latLng;
    if (!latLng) return;

    const coords = { lat: latLng.lat(), lng: latLng.lng() };
    setInternalMarker(coords);

    if (geocoderRef.current) {
      geocoderRef.current.geocode({ location: latLng }, (results, status) => {
        if (status === "OK" && results?.[0]) {
          const address = results[0].formatted_address;
          const components = parseAddressComponents(results[0].address_components);
          setInternalAddress(address);
          setPlaceSearchInputValue(address);
          onMarkerChange?.(coords, address, components);
          onAddressChange?.(address);
        } else {
          const fallback = `${coords.lat.toFixed(6)}, ${coords.lng.toFixed(6)}`;
          setInternalAddress(fallback);
          onMarkerChange?.(coords, fallback);
          onAddressChange?.(fallback);
        }
      });
    }
  }, [onMarkerChange, onAddressChange]);

  const markerIconStyle = useMemo(() => isLoaded ? {
    url: "data:image/svg+xml;base64," + btoa(`
      <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
        <circle cx="16" cy="16" r="12" fill="#ef4444" stroke="#ffffff" stroke-width="3"/>
        <circle cx="16" cy="16" r="4" fill="#ffffff"/>
      </svg>
    `),
    scaledSize: new google.maps.Size(32, 32),
    anchor: new google.maps.Point(16, 16),
  } : undefined, [isLoaded]);

  const renderMap = (height = "400px") => (
    <GoogleMap
      mapContainerStyle={{ width: "100%", height, borderRadius: "8px" }}
      center={internalMarker || defaultCenter}
      zoom={internalMarker ? 16 : 13}
      onClick={handleMapClick}
      onLoad={(map) => (mapRef.current = map)}
      options={{
        streetViewControl: false,
        mapTypeControl: false,
        fullscreenControl: false,
        zoomControl: true,
        gestureHandling: "greedy",
        styles: [
          { featureType: "poi", elementType: "labels", stylers: [{ visibility: "off" }] },
          { featureType: "transit", elementType: "labels", stylers: [{ visibility: "off" }] },
        ],
      }}
    >
      {internalMarker && (
        <Marker
          position={internalMarker}
          icon={markerIconStyle}
          animation={google.maps.Animation.DROP}
          title="Seçili Konum"
        />
      )}
    </GoogleMap>
  );

  if (!visible) return null;

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center h-40 bg-white">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-slate-100">
            <svg className="w-8 h-8 text-slate-400 animate-spin" viewBox="0 0 24 24" fill="none">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z" />
            </svg>
          </div>
          <p className="text-slate-500 font-medium mt-2">Google Maps yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="bg-white p-2 mb-2 flex items-center gap-2">
        <EnvironmentOutlined className="text-gray-400" />
        {marker && (
          <Button
            size="small"
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={clearAddressData}
          >
            Temizle
          </Button>
        )}
        {internalMarker && (
          <span className="text-xs text-green-600 ml-2">
           <span className="text-xs text-green-600 ml-2">
  ✓ Pin:{" "}
  {typeof internalMarker?.lat === "string"
    ? parseFloat(internalMarker.lat).toFixed(4)
    : internalMarker?.lat?.toFixed(4)
  },{" "}
  {typeof internalMarker?.lng === "string"
    ? parseFloat(internalMarker.lng).toFixed(4)
    : internalMarker?.lng?.toFixed(4)
  }
</span>

          </span>
        )}
      </div>

      {showInlineMap && (
        <div className="border border-gray-300 overflow-hidden mb-4">
          {renderMap(compactMode ? "200px" : "300px")}
        </div>
      )}

      <Modal
        title="Harita Üzerinden Konum Seçin"
        open={showMapPopup}
        onOk={() => setShowMapPopup(false)}
        onCancel={() => setShowMapPopup(false)}
        width={800}
        footer={null}
      >
        {renderMap("400px")}
      </Modal>
    </div>
  );
};

export default SimpleMapComponent;
