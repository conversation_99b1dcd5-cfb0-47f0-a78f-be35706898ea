import React, { useCallback, useMemo, useState, useEffect, useRef } from "react";
import { createEditor, Descendant, Editor, Transforms, Node } from "slate";
import {
  Slate,
  Editable,
  withReact,
  RenderElementProps,
  RenderLeafProps,

} from "slate-react";
import { withHistory } from "slate-history";
import Toolbar from "./Toolbar";
import Leaf from "./Components/Leaf";
import Element from "./Components/Element";
import {
  loadUsers,
  extractMentionsFromEditor,
} from "./Helpers/Mentions";
import { getUserListFilter } from "@/apps/Admin/Pages/Users/<USER>";
import { SlateEditorProps } from "./Models/Types";
import DropdownSection from "./Components/DropdownSection";
import UserSection from "./Components/UserSection";
import { useTranslation } from "react-i18next";


const SlateEditor: React.FC<SlateEditorProps> = ({
  value,
  onChange,
  onChangeMention,
  placeholder,
  className,
  isShowUserSectionMention,
  readOnly = false,
  showToolbar = true,
  showMentions = true,
  uploadPcFolderPath,
  watchListIds,
  form,fieldName
}) => {
  const [editorValue, setEditorValue] = useState<Descendant[]>(
    value || [
      {
        type: "paragraph",
        children: [{ text: "" }],
      },
    ]
  );

  const editor = useMemo(() => {
    const e = withHistory(withReact(createEditor()));

    // Define which elements are void (cannot contain text)
    const { isVoid, isInline } = e;

    e.isVoid = (element) => {
      return element.type === "mention" || element.type === "emoji"
        ? true
        : isVoid(element);
    };

    e.isInline = (element) => {
      return element.type === "mention" || element.type === "emoji"
        ? true
        : isInline(element);
    };

    return e;
  }, []);

  // Mention dropdown states
  const [mentionSearch, setMentionSearch] = useState("");
  const [showMentionDropdown, setShowMentionDropdown] = useState(false);
  const [mentionUsers, setMentionUsers] = useState<any[]>([]);
  const [mentionPosition, setMentionPosition] = useState({ top: 0, left: 0 });
  const{t} = useTranslation()
  // Add mentions section users
  const [addMentionUsers, setAddMentionUsers] = useState<any[]>([]);
  const [allUsers, setAllUsers] = useState<any[]>([]); // Store all users for filtering

  // Track mentions in editor
  const [editorMentions, setEditorMentions] = useState<
    Array<{ Name: string; Surname: string; Id: string }>
  >([]);


  // Load users for add mentions section and mention dropdown (only if mentions are enabled)
  useEffect(() => {
    // Check if we should show user section and have watchListIds
    if (isShowUserSectionMention && watchListIds && watchListIds.length > 0) {
      // Load specific users from watchListIds
      getUserListFilter({ PageNumber: 1, PageSize: 100, Ids: watchListIds }).then((res) => {
        if (res.Value?.length > 0) {
          setAddMentionUsers(res.Value);
         
        } else {
          
        }
      }).catch((error) => {
        
      });
    } else {
      // Load all users as before
      loadUsers(setAddMentionUsers, setAllUsers);
    }
  }, [isShowUserSectionMention, watchListIds]);

  // Debounce timer ref
  const debounceTimerRef = useRef<number | null>(null);

  // Actual search function (without debounce)
  const performMentionSearch = async (searchTerm: string) => {
    try {
     

      const params: any = { PageNumber: 1, PageSize: 100 };

      // If searchTerm is not empty, add SearchTerm parameter (don't use Ids)
      if (searchTerm.trim()) {
        params.SearchTerm = searchTerm.trim();
       
      } else {
        // Only add watchListIds if searchTerm is empty
        if (watchListIds && watchListIds.length > 0) {
          params.Ids = watchListIds;
         
        }
      }

   
      const response = await getUserListFilter(params);

      if (response.Value && response.Value.length > 0) {
        const users = response.Value.map((user: any) => ({
          id: user.Id,
          name: user.Name,
          surname: user.Surname,
          fullName: `${user.Name} ${user.Surname}`,
          avatar: user.Avatar || null,
          email:user.Email
        }));
       
        setMentionUsers(users);

        // Update allUsers array with new users (merge without duplicates)
        setAllUsers(prevAllUsers => {
          const existingIds = new Set(prevAllUsers.map((u: any) => u.id));
          const newUsers = users.filter((u: any) => !existingIds.has(u.id));
          const updatedAllUsers = [...prevAllUsers, ...newUsers];
        
          return updatedAllUsers;
        });
      } else {
       
        setMentionUsers([]);
      }
    } catch (error) {
     
      setMentionUsers([]);
    }
  };

  // Debounced search function
  const searchMentionUsers = (searchTerm: string) => {
    // Clear previous timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // For empty search term (just @), search immediately
    if (!searchTerm.trim()) {
      performMentionSearch(searchTerm);
      return;
    }

    // For search terms, debounce for 1 second
   
    debounceTimerRef.current = window.setTimeout(() => {
      performMentionSearch(searchTerm);
    }, 1000);
  };

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  const renderElement = useCallback(
    (props: RenderElementProps) => <Element {...props} />,
    []
  );
  const renderLeaf = useCallback(
    (props: RenderLeafProps) => <Leaf {...props} />,
    []
  );

  const handleChange = (newValue: Descendant[]) => {
   
    setEditorValue(newValue);

    // Extract and track mentions
    const mentions = extractMentionsFromEditor(newValue, allUsers);
    setEditorMentions(mentions);

  
   

    // Call onChangeMention callback if provided
    if (onChangeMention) {
      onChangeMention(mentions);
    }

    onChange?.(newValue);
  };

  // Handle click on empty area to add paragraph
  const handleEditorClick = (e: React.MouseEvent) => {
    const { selection } = editor;

    // If clicking in empty area or at the end
    if (!selection) {
      // Add a new paragraph at the end
      const lastPath = [editorValue.length];
      const newParagraph = {
        type: "paragraph",
        children: [{ text: "" }],
      };

      Transforms.insertNodes(editor, newParagraph, { at: lastPath });
      Transforms.select(editor, {
        anchor: { path: [...lastPath, 0], offset: 0 },
        focus: { path: [...lastPath, 0], offset: 0 },
      });
    }
  };

  // Handle mention detection and task-list navigation
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
    

      // Handle mention search when typing after @ (only if mentions are enabled)
      if (showMentions && 
        showMentionDropdown &&
        event.key !== "Escape" &&
        event.key !== "Enter" &&
        event.key !== "ArrowUp" &&
        event.key !== "ArrowDown"
      ) {
        // Get current text to extract search term
        setTimeout(() => {
          const { selection } = editor;
          if (selection) {
            try {
              // Get the current node text
              const [node] = Editor.node(editor, selection);
              const nodeText = Node.string(node);

              // Find the last @ symbol in current node
              const lastAtIndex = nodeText.lastIndexOf("@");
              if (lastAtIndex !== -1) {
                // Get cursor position in the node
                const { offset } = selection.anchor;

                // Extract search term from @ to cursor position
                const searchTerm = nodeText.substring(lastAtIndex + 1, offset);
          

                // Clean the search term (remove extra spaces, etc.)
                const cleanSearchTerm = searchTerm.trim();
         

                setMentionSearch(cleanSearchTerm);

                // Update dropdown position while typing
                try {
                  const domSelection = window.getSelection();
                  if (domSelection && domSelection.rangeCount > 0) {
                    const range = domSelection.getRangeAt(0);
                    const rect = range.getBoundingClientRect();

                    // Get editor container position
                    const editorElement = document.querySelector('[data-slate-editor="true"]');
                    const editorRect = editorElement?.getBoundingClientRect();

                    if (editorRect) {
                      setMentionPosition({
                        top: rect.bottom - editorRect.top + 15, // 15px below cursor
                        left: rect.left - editorRect.left,
                      });
                    }
                  }
                } catch (error) {
                  console.error('Error updating mention position:', error);
                }

                // Search users with API call
                searchMentionUsers(cleanSearchTerm);
              } else {
                // No @ found, close dropdown
                setShowMentionDropdown(false);
              }
            } catch (error) {
              console.error("Error extracting mention search term:", error);
            }
          }
        }, 10); // Small delay to ensure text is updated
      }

      // Handle Enter key in task-items and code blocks
      if (event.key === "Enter") {
        const { selection } = editor;
        if (selection) {
          // Check if we're in a code block or HTML block - allow normal Enter behavior
          const [codeMatch] = Editor.nodes(editor, {
            match: (n) =>
              (n as any).type === "code-block" ||
              (n as any).type === "html-block",
          });

          if (codeMatch) {
            // Allow normal Enter behavior in code blocks and HTML blocks
            return;
          }

          // Handle info-panel Enter behavior - insert line break instead of new panel
          const [infoPanelMatch] = Editor.nodes(editor, {
            match: (n) => (n as any).type === "info-panel",
          });

          if (infoPanelMatch) {
            event.preventDefault();
            // Insert a line break (soft break) instead of creating new panel
            editor.insertText("\n");
            return;
          }

          // Handle quote Enter behavior - insert line break instead of new quote
          const [quoteMatch] = Editor.nodes(editor, {
            match: (n) => (n as any).type === "quote",
          });

          if (quoteMatch) {
            event.preventDefault();
            // Insert a line break (soft break) instead of creating new quote
            editor.insertText("\n");
            return;
          }

          // Handle task-item Enter behavior
          const [taskMatch] = Editor.nodes(editor, {
            match: (n) => (n as any).type === "task-item",
          });

          if (taskMatch) {
            const [node, path] = taskMatch;
            const nodeText = (node as any).children[0]?.text || "";

            // If task-item is empty and Enter is pressed, exit task-list
            if (nodeText.trim() === "") {
              event.preventDefault();

              // Remove the empty task-item
              Transforms.removeNodes(editor, { at: path });

              // Add a normal paragraph after the task-list
              const taskListPath = path.slice(0, -1); // Get parent task-list path
              const afterTaskListPath = [taskListPath[0] + 1];

              const newParagraph = {
                type: "paragraph",
                children: [{ text: "" }],
              };

              Transforms.insertNodes(editor, newParagraph, {
                at: afterTaskListPath,
              });
              Transforms.select(editor, {
                anchor: { path: [...afterTaskListPath, 0], offset: 0 },
                focus: { path: [...afterTaskListPath, 0], offset: 0 },
              });
              return;
            }
          }
        }
      }

      // Check if user typed @ (only if mentions are enabled)
      if (event.key === "@" && showMentions) {
      

        // Calculate cursor position for dropdown
        setTimeout(() => {
          try {
            const domSelection = window.getSelection();
            if (domSelection && domSelection.rangeCount > 0) {
              const range = domSelection.getRangeAt(0);
              const rect = range.getBoundingClientRect();

              // Get editor container position
              const editorElement = document.querySelector('[data-slate-editor="true"]');
              const editorRect = editorElement?.getBoundingClientRect();

              if (editorRect) {
                setMentionPosition({
                  top: rect.bottom - editorRect.top + 15, // 15px below cursor
                  left: rect.left - editorRect.left,
                });
              
              } else {
                // Fallback to fixed position
                setMentionPosition({ top: 50, left: 20 });
              }
            } else {
              // Fallback to fixed position
              setMentionPosition({ top: 50, left: 20 });
            }
          } catch (error) {
            console.error('Error calculating mention position:', error);
            // Fallback to fixed position
            setMentionPosition({ top: 50, left: 20 });
          }
        }, 0); // Small delay to ensure DOM is updated

        // Reset search and show all users
        setMentionSearch("");

        // Search all users (empty search term)
        searchMentionUsers("");

        setShowMentionDropdown(true);
      }

      // Handle backspace - check if we should close dropdown (only if mentions are enabled)
      if (showMentions && event.key === "Backspace" && showMentionDropdown) {
        setTimeout(() => {
          const { selection } = editor;
          if (selection) {
            try {
              const [node] = Editor.node(editor, selection);
              const nodeText = Node.string(node);

              // If no @ symbol found, close dropdown
              if (!nodeText.includes("@")) {
                setShowMentionDropdown(false);
                setMentionSearch("");
              }
            } catch (error) {
              console.error("Error checking backspace:", error);
            }
          }
        }, 10);
      }

      // Handle escape to close dropdown (only if mentions are enabled)
      if (showMentions && event.key === "Escape" && showMentionDropdown) {
        setShowMentionDropdown(false);
        setMentionSearch("");
        event.preventDefault();
      }
    },
    [showMentionDropdown, editor]
  );

  useEffect(()=>{
    console.log("editor",editorValue)
    if(form)
    {
      form.setFieldValue(fieldName,editorValue||[])

    }

  },[editorValue])

  return (
    <div
      className={`border border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm ${
        className || ""
      }`}
    >
      <Slate
        editor={editor}
        initialValue={editorValue}
        onValueChange={handleChange}
      >
        {showToolbar && <Toolbar uploadPcFolderPath={uploadPcFolderPath} editorValue={editor} setEditorValue={setEditorValue}  />}
        <div className={`p-4 relative ${readOnly ? 'border':''}"`}>
          <Editable
            renderElement={renderElement}
            renderLeaf={renderLeaf}
            placeholder={placeholder||t("editor.slateEditorPlaceholder")}
            spellCheck
                        readOnly={readOnly}
            autoFocus
            className={`outline-none text-gray-900 leading-relaxed ${
              readOnly ? 'min-h-fit' : 'min-h-[200px]'
            }`}           
              style={{
              fontSize: "14px",
              lineHeight: "1.6",
              fontFamily:
                '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
            }}
            onClick={handleEditorClick}
            onKeyDown={(event) => {
              // Handle mention detection first
              handleKeyDown(event);

              
            }}
          />

          {/* Mention Dropdown */}
          {showMentions && (
            <DropdownSection
              showMentionDropdown={showMentionDropdown}
              mentionPosition={mentionPosition}
              mentionUsers={mentionUsers}
              mentionSearch={mentionSearch}
              setShowMentionDropdown={setShowMentionDropdown}
              setMentionSearch={setMentionSearch}
            />
          )}
        </div>

        {isShowUserSectionMention && showMentions && <UserSection addMentionUsers={addMentionUsers} />}


      </Slate>
    </div>
  );
};

export default SlateEditor;
