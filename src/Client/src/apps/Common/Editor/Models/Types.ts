import { Editor, Descendant } from 'slate';

// Extend Slate's types
declare module 'slate' {
  interface CustomTypes {
    Element: CustomElement;
    Text: CustomText;
  }
}

// Custom types
export type CustomEditor = Editor;

export interface CustomText {
  text: string;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  code?: boolean;
  heading1?: boolean;
  heading2?: boolean;
  heading3?: boolean;
  color?: string;
  [key: string]: any;
}

export interface CustomElement {
  type: string;
  children: (CustomText | CustomElement)[];
  url?: string;
  character?: string;
  checked?: boolean;
  width?: number;
  height?: number;
  align?: 'left' | 'center' | 'right';
  alt?: string;
  caption?: string;
  userId?: string;
  userName?: string;
}

// Hotkeys
export const HOTKEYS: Record<string, keyof Omit<CustomText, 'text'>> = {
  'mod+b': 'bold',
  'mod+i': 'italic',
  'mod+u': 'underline',
  'mod+`': 'code',
};

export interface SlateEditorProps {
  value?: Descendant[];
  onChange?: (value: Descendant[]) => void;
  onChangeMention?: (mentions: Array<{Name: string, Surname: string, Id: string}>) => void;
  placeholder?: string;
  className?: string;
  readOnly?: boolean;
  isShowUserSectionMention: boolean;
  showToolbar?: boolean;
  showMentions?: boolean;
  uploadPcFolderPath:string
  watchListIds:string[],
  form?:any,
  fieldName?:string

}
