import React from 'react';
import {  Space, Divider,  } from 'antd';
import History from './Components/History';
import Voice from './Components/Voice';
import TextStyle from './Components/Text/TextStyle';
import Bold from './Components/Text/Bold';
import Italic from './Components/Text/Italic';
import Underline from './Components/Text/Underline';
import TextColor from './Components/Text/TextColor';
import OrderedList from './Components/List/OrderedList';
import UnorderedList from './Components/List/UnorderedList';
import CheckList from './Components/CheckList/CheckList';
import Link from './Components/Link';
import Emoji from './Components/Emoji';
import Table from './Components/Table/Table';
import Code from './Components/Code/Code';
import More from './Components/More/More';
import FileManager from './Components/FileManager';

interface ToolbarProps {
  className?: string;
  uploadPcFolderPath:string,
  editorValue:any;
  setEditorValue:any
}

const Toolbar: React.FC<ToolbarProps> = ({ className,uploadPcFolderPath,editorValue,setEditorValue }) => {
 

  return (
    <div className={`flex items-center gap-1 px-4 py-3 border-b border-gray-200 bg-gray-50 ${className || ''}`}>
      {/* Undo/Redo Buttons */}
      <History />

      <Divider type="vertical" className="h-6" />

      {/* Voice Recognition Button */}
      <Voice />

      <Divider type="vertical" className="h-6" />

      {/* Text Style Dropdown */}
      <TextStyle />

      <Divider type="vertical" className="h-6" />

      {/* Text Formatting */}
      <Space size={0}>
        <Bold />
        <Italic />
        <Underline />

        {/* Text Color */}
        <TextColor />

       
      </Space>

   

      <Divider type="vertical" className="h-6" />

      {/* Lists */}
      <Space size={0}>
        <UnorderedList />
        <OrderedList />
        <CheckList />
      </Space>

      <Divider type="vertical" className="h-6" />

      {/* Insert Elements */}
      <Space size={0}>
        <Link />

        {/* Image */}
        <FileManager uploadPcFolderPath={uploadPcFolderPath} editorValue={editorValue} setEditorValue={setEditorValue} />

        {/* Mention */}
        {/* <Mention /> */}

        {/* Emoji */}
        <Emoji />

        {/* Table */}
        <Table />

        {/* Code */}
        <Code />
      </Space>

      <Divider type="vertical" className="h-6" />

      {/* More */}
     

      <More />
      

   

    </div>
  );
};

export default Toolbar;