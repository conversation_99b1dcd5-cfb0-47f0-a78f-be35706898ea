import React, { useState, } from 'react';
import {
  PictureOutlined,
} from '@ant-design/icons';
import { Button, Dropdown, Modal, Input, Tooltip } from 'antd';
import { useSlate, ReactEditor } from 'slate-react';
import { Editor, Transforms } from 'slate';
import FileManagerIndex from '../../../FileManager/FileManagerIndex';
import { setBackEndUrl } from '@/helpers/SetBackEndUrl';
import { handleResetAllFieldsFolder } from '@/apps/FileManager/ClientSideStates';
import { useDispatch } from 'react-redux';
import { useQueryClient } from 'react-query';
import fileManagerEndPoints from "@/apps/FileManager/EndPoints"
import { bulkUploadFile, getFilesListFilter } from '@/apps/FileManager/Services';
import { useTranslation } from 'react-i18next';
import { showErrorCatching } from '@/helpers/ShowErrorCatching';
import ScannerIndex from '@/apps/FileManager/Components/Scanner.tsx/ScannerIndex';

const FileManager: React.FC<{uploadPcFolderPath:string,editorValue:any,setEditorValue:any}> = ({uploadPcFolderPath,setEditorValue}) => {
  const {t} = useTranslation()
  const queryClient = useQueryClient()
  const editor = useSlate();
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const [isImageUrlModalOpen, setIsImageUrlModalOpen] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [isFileManagerModalOpen, setIsFileManagerModalOpen] = useState(false);
  const dispatch = useDispatch();
 const [isShowScanner, setIsShowScanner] = useState(false);
 
  const openImageUrlModal = () => {
    setIsImageUrlModalOpen(true);
    setImageUrl('');
  };

  const closeImageUrlModal = () => {
    setIsImageUrlModalOpen(false);
    setImageUrl('');
  };

  const handleImageUrlSubmit = () => {
    if (imageUrl.trim()) {
      // Close modal first
      closeImageUrlModal();

      // Use setTimeout to ensure modal is closed and editor is ready
      setTimeout(() => {
        try {
          // Ensure editor has focus and selection
          ReactEditor.focus(editor as any);

          // If no selection, set cursor at the end
          if (!editor.selection) {
            const endPoint = Editor.end(editor, []);
            Transforms.select(editor, endPoint);
          }

          const image = {
            type: 'image', // Always use 'image' type for Slate
            url: imageUrl.trim(),
            alt: '',
            children: [{ text: '' }]
          };

          // Insert the image node
          Transforms.insertNodes(editor, image);

          // Always add a paragraph after the image
          const paragraph = { type: 'paragraph', children: [{ text: '' }] };
          Transforms.insertNodes(editor, paragraph);

          setEditorValue([...editor?.children]);

         
        } catch (error) {
          console.error('Error inserting image from URL:', error);
        }
      }, 100); // Small delay to ensure modal is closed
    }
  };

  // File Manager Modal functions
  const openFileManagerModal = () => {
    queryClient.resetQueries({
      queryKey: fileManagerEndPoints.getFolderListFilter,
      exact: false,
    });
    setIsFileManagerModalOpen(true);
  };

  const closeFileManagerModal = () => {
    setIsFileManagerModalOpen(false);
     dispatch(
                          handleResetAllFieldsFolder()
                        );
                      
  };

  const handleFileManagerSelection = (selectedFiles: any) => {
    let files = selectedFiles ? Object.values(selectedFiles) : [];

   

    // Close modal first
    closeFileManagerModal();

    // Use setTimeout to ensure modal is closed and editor is ready
    setTimeout(() => {
      try {
        // Ensure editor has focus and selection
        ReactEditor.focus(editor as any);

        // If no selection, set cursor at the end
        if (!editor.selection) {
          const endPoint = Editor.end(editor, []);
          Transforms.select(editor, endPoint);
        }

        files.forEach((file: any, index: number) => {
          const imageUrl = `${setBackEndUrl()}/Uploads/${file.StoragePath}`;
          const image = {
            type: 'image', // Always use 'image' type for Slate
            url: imageUrl,
            alt: file.Name || '',
            children: [{ text: '' }]
          };

         
          Transforms.insertNodes(editor, image);

          const paragraph = { type: 'paragraph', children: [{ text: '' }] };
          Transforms.insertNodes(editor, paragraph);
        });

        setEditorValue([...editor?.children]);

       
      } catch (error) {
        console.error('Error inserting images:', error);
      }
    }, 100); // Small delay to ensure modal is closed
  };

   const handleUploadFromComputer = async (event: React.ChangeEvent<HTMLInputElement>) => {
          const files = event.target.files;
          if (!files || files.length === 0) return;

       

          const formData = new FormData();

          formData.append("FolderPath", uploadPcFolderPath);

          Array.from(files).forEach(file => {
              formData.append("Files", file);
          });

          try {
              const res = await bulkUploadFile(formData);

              if (res?.Value?.length > 0) {

                  const uploadedFiles = await getFilesListFilter({PageSize:100,fileIds:res?.Value})

                  if (uploadedFiles?.Value?.length > 0) {
                    // Use setTimeout to ensure editor is ready
                    setTimeout(() => {
                      try {
                        // Ensure editor has focus and selection
                        ReactEditor.focus(editor as any);

                        // If no selection, set cursor at the end
                        if (!editor.selection) {
                          const endPoint = Editor.end(editor, []);
                          Transforms.select(editor, endPoint);
                        }

                        uploadedFiles.Value.forEach((file: any) => {
                          const imageUrl = `${setBackEndUrl()}/Uploads/${file.StoragePath}`;
                          const image = {
                            type: 'image', // Always use 'image' type for Slate
                            url: imageUrl,
                            alt: file.Name || '',
                            children: [{ text: '' }]
                          };

                          // Insert the image node
                          Transforms.insertNodes(editor, image);

                         
                          const paragraph = { type: 'paragraph', children: [{ text: '' }] };
                          Transforms.insertNodes(editor, paragraph);
                        });

                      
                        setEditorValue([...editor?.children]);
                      
                      } catch (error) {
                        console.error('Error inserting images from computer:', error);
                      }
                    }, 100); // Small delay to ensure upload is complete
                  }
              }
          } catch (err) {
              showErrorCatching(err, null, false, t);
          }

          // Reset file input
          event.target.value = '';
      };

  return (
    <>
      {/* Image Dropdown */}
      <Dropdown
        trigger={['click']}
        placement="bottomLeft"
        menu={{
          items: [
            {
              key: 'file-manager',
              label: (
                <div className="flex items-center gap-3 py-2">
                  <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                    <span className="text-blue-600 font-bold text-xs">📁</span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{t("editor.uploadFromFileManagerTitle")}</div>
                    <div className="text-xs text-gray-500">{t("editor.uploadFromFileManagerDesc")}</div>
                  </div>
                </div>
              ),
              onClick: () => {
                openFileManagerModal();
              }
            },
            {
              key: 'computer-upload',
              label: (
                <div className="flex items-center gap-3 py-2">
                  <div className="w-8 h-8 bg-green-100 rounded flex items-center justify-center">
                    <span className="text-green-600 font-bold text-xs">💻</span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{t("editor.uploadFromComputerTitle")}</div>
                    <div className="text-xs text-gray-500">{t("editor.uploadFromComputerDesc")}</div>
                  </div>
                </div>
              ),
              onClick: () => {
                // Computer upload logic will be added
                fileInputRef.current?.click()
              }
            },
            {
              key: 'url-input',
              label: (
                <div className="flex items-center gap-3 py-2">
                  <div className="w-8 h-8 bg-purple-100 rounded flex items-center justify-center">
                    <span className="text-purple-600 font-bold text-xs">🔗</span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{t("editor.uploadByUrlTitle")}</div>
                    <div className="text-xs text-gray-500">{t("editor.uploadByUrlDesc")}</div>
                  </div>
                </div>
              ),
              onClick: () => {
                openImageUrlModal();
              }
            },
            {
              key: "scanner",
              label: (
                <div className="flex items-center gap-3 py-2">
                  <div className="w-8 h-8 bg-red-100 rounded flex items-center justify-center">
                    <span className="text-blue-600 font-bold text-xs">
                    📇
                    </span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">
                      {t("fileManager.scanFile")}
                    </div>
                    <div className="text-xs text-gray-500">
                      {t("fileManager.scanFileDesc2")}
                    </div>
                  </div>
                </div>
              ),
              onClick: () => {
                setIsShowScanner(true);
              },
            },

          ]
        }}
      >
        <Tooltip title={t(("editor.uploadImage"))}>
          <Button
            type="text"
            size="small"
            className="flex items-center justify-center w-8 h-8"
          >
            <PictureOutlined />
          </Button>
        </Tooltip>
      </Dropdown>


      <input
    type="file"
    multiple={true}
    ref={fileInputRef}
    style={{ display: "none" }}
    onChange={handleUploadFromComputer}
    
  />

      {/* Image URL Modal */}
      <Modal
        title={t("editor.uploadByUrlTitle")}
        open={isImageUrlModalOpen}
        onOk={handleImageUrlSubmit}
        onCancel={closeImageUrlModal}
        okText={t("editor.add")}
        cancelText={t("editor.cancel")}
        width={500}
      >
        <div className="py-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
          {t("editor.uploadByUrlTitle")}
          </label>
          <Input
            placeholder="https://example.com/image.jpg"
            value={imageUrl}
            onChange={(e) => setImageUrl(e.target.value)}
            onPressEnter={handleImageUrlSubmit}
            autoFocus
          />
          <div className="mt-2 text-xs text-gray-500">
            {t("editor.imageUrlInputDesc")}
          </div>
        </div>
      </Modal>

      {/* File Manager Modal */}
      <Modal
        title={t("editor.uploadFromFileManagerTitle")}
        open={isFileManagerModalOpen}
        onCancel={closeFileManagerModal}
        footer={null}
        width={"90%"}
        style={{ top: 20 }}
        okText={t("editor.add")}
        cancelText={t("editor.cancel")}
      >
        <FileManagerIndex
          onFinishSelectFile={handleFileManagerSelection}
        />
      </Modal>
      <Modal
        open={isShowScanner}
        onCancel={() => {
          setIsShowScanner(false);
        }}
        destroyOnHidden
        footer={false}
        style={{ top: 0 }}
        width="50%"
      >
        <ScannerIndex
          folderPath={uploadPcFolderPath}
          onFinishSelectFile={(files:any[])=>{
            handleFileManagerSelection(files)
            setIsShowScanner(false);
          }}
        />
      </Modal>
    </>
  );
};

export default FileManager;
