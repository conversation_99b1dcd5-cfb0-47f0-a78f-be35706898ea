import React from 'react';
import { RenderElementProps } from 'slate-react';
import { TableElement, TableRowElement, TableCellElement } from './Table';
import { CodeBlockElement, HtmlBlockElement } from './Code';
import MentionElement from './MentionElement';
import ImageElement from './ImageElement';
import { InfoPanelElement, QuoteElement, DividerElement } from './More';
import { CheckListItem } from './CheckList/CheckList';
import { CustomElement } from '../Models/Types';

// Element renderer
const Element: React.FC<RenderElementProps> = ({ attributes, children, element,readOnly }) => {
  const customElement = element as CustomElement;

  // Get alignment style
  const getAlignmentStyle = () => {
    const align = (customElement as any).align;
    if (align === 'center') return { textAlign: 'center' as const };
    if (align === 'right') return { textAlign: 'right' as const };
    if (align === 'left') return { textAlign: 'left' as const };
    return {};
  };

  const alignmentStyle = getAlignmentStyle();

  switch (customElement.type) {
    case 'heading1':
      return (
        <h1 {...attributes} className="text-3xl font-bold mb-4" style={alignmentStyle}>
          {children}
        </h1>
      );
    case 'heading2':
      return (
        <h2 {...attributes} className="text-2xl font-bold mb-3" style={alignmentStyle}>
          {children}
        </h2>
      );
    case 'heading3':
      return (
        <h3 {...attributes} className="text-xl font-bold mb-2" style={alignmentStyle}>
          {children}
        </h3>
      );
    case 'quote':
      return (
        <blockquote {...attributes} className="border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4">
          {children}
        </blockquote>
      );
    case 'code-block':
      return <CodeBlockElement attributes={attributes} element={customElement} children={children} />;
    case 'html-block':
      return <HtmlBlockElement attributes={attributes} element={customElement} children={children} />;
    case 'info-panel':
      return <InfoPanelElement attributes={attributes} element={customElement} children={children} />;
    case 'quote':
      return <QuoteElement attributes={attributes} element={customElement} children={children} />;
    case 'divider':
      return <DividerElement attributes={attributes} element={customElement} children={children} />;

    case 'bulleted-list':
      return (
        <ul {...attributes} className="list-disc list-inside my-4">
          {children}
        </ul>
      );
    case 'numbered-list':
      return (
        <ol {...attributes} className="list-decimal list-inside my-4">
          {children}
        </ol>
      );
    case 'task-list':
      return (
        <ul {...attributes} className="my-4 space-y-1">
          {children}
        </ul>
      );
    case 'list-item':
      return (
        <li {...attributes} className="mb-1">
          {children}
        </li>
      );
    case 'task-item':
      return <CheckListItem attributes={attributes} element={customElement} children={children} />;
    case 'link':
      return (
        <a
          {...attributes}
          href={customElement.url}
          className="text-blue-600 underline hover:text-blue-800"
          target="_blank"
          rel="noopener noreferrer"
        >
          {children}
        </a>
      );
    case 'image':
      return <ImageElement attributes={attributes} element={customElement} children={children} isShowOptions={readOnly} />;
    case 'mention':
      return <MentionElement attributes={attributes} element={customElement} children={children} />;
    case 'emoji':
      return (
        <span
          {...attributes}
          className="text-lg inline-block mx-1 cursor-pointer hover:bg-gray-100 rounded px-1"
          contentEditable={false}
          onKeyDown={(e) => {
            if (e.key === 'Backspace' || e.key === 'Delete') {
              // Allow deletion
              e.stopPropagation();
            }
          }}
        >
          {customElement.character}
          {children}
        </span>
      );
    case 'table':
      return <TableElement attributes={attributes} element={customElement} children={children} />;
    case 'table-row':
      return <TableRowElement attributes={attributes} element={customElement} children={children} />;
    case 'table-cell':
      return <TableCellElement attributes={attributes} element={customElement} children={children} />;
    default:
      return (
        <p {...attributes} className="mb-2" style={alignmentStyle}>
          {children}
        </p>
      );
  }
};

export default Element;
