import React, { useState } from 'react';
import { useSlate, ReactEditor } from 'slate-react';
import { Transforms } from 'slate';
import { Button, Input, Select, Popover } from 'antd';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { CustomElement } from '../Models/Types';

// Image Element Component
const ImageElement: React.FC<{
  attributes: any;
  element: CustomElement;
  children: any;
  isShowOptions:boolean
}> = ({ attributes, element, children,isShowOptions }) => {
  const editor = useSlate();
  const [isEditing, setIsEditing] = useState(false);
  const [imageProps, setImageProps] = useState({
    width: element.width || 300,
    height: element.height || 200,
    align: element.align || 'left',
    alt: element.alt || '',
    caption: element.caption || ''
  });

  const handleSave = () => {
    // For now, just close the editing mode
    // In a real implementation, you would update the element
    setIsEditing(false);
  };

  const handleDelete = () => {
    const path = ReactEditor.findPath(editor as any, element);
    Transforms.removeNodes(editor, { at: path });
  };

  const alignmentClass = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  }[imageProps.align];

  const imageControls = (
    <div className="p-4 space-y-3">
      <div>
        <label className="block text-sm font-medium mb-1">Width:</label>
        <Input
          type="number"
          value={imageProps.width}
          onChange={(e) => setImageProps({...imageProps, width: parseInt(e.target.value) || 300})}
          className="w-full"
        />
      </div>
      <div>
        <label className="block text-sm font-medium mb-1">Height:</label>
        <Input
          type="number"
          value={imageProps.height}
          onChange={(e) => setImageProps({...imageProps, height: parseInt(e.target.value) || 200})}
          className="w-full"
        />
      </div>
      <div>
        <label className="block text-sm font-medium mb-1">Alignment:</label>
        <Select
          value={imageProps.align}
          onChange={(value) => setImageProps({...imageProps, align: value})}
          className="w-full"
        >
          <Select.Option value="left">Left</Select.Option>
          <Select.Option value="center">Center</Select.Option>
          <Select.Option value="right">Right</Select.Option>
        </Select>
      </div>
      <div>
        <label className="block text-sm font-medium mb-1">Alt Text:</label>
        <Input
          value={imageProps.alt}
          onChange={(e) => setImageProps({...imageProps, alt: e.target.value})}
          placeholder="Image description"
          className="w-full"
        />
      </div>
      <div>
        <label className="block text-sm font-medium mb-1">Caption:</label>
        <Input
          value={imageProps.caption}
          onChange={(e) => setImageProps({...imageProps, caption: e.target.value})}
          placeholder="Image caption"
          className="w-full"
        />
      </div>
      <div className="flex gap-2">
        <Button type="primary" size="small" onClick={handleSave}>Save</Button>
        <Button size="small" onClick={() => setIsEditing(false)}>Cancel</Button>
      </div>
    </div>
  );

  return (
    <div {...attributes} className={`my-4 ${alignmentClass}`}>
      <div className="relative inline-block group">
        <img
          src={element.url}
          alt={imageProps.alt}
          style={{
            width: `${imageProps.width}px`,
            height: `${imageProps.height}px`,
            objectFit: 'cover'
          }}
          className="rounded-md border border-gray-200"
        />

        {/* Image Controls */}
        {
          isShowOptions&&
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
          <Popover
            content={imageControls}
            title="Image Settings"
            trigger="click"
            open={isEditing}
            onOpenChange={setIsEditing}
          >
            <Button size="small" icon={<EditOutlined />} className="bg-white shadow-md" title="Edit image" />
          </Popover>

          <Button
            size="small"
            icon={<DeleteOutlined />}
            className="bg-white shadow-md hover:bg-red-50 hover:border-red-300"
            onClick={handleDelete}
            title="Delete image"
            danger
          />
        </div>
        }

        {/* Caption */}
        {imageProps.caption && (
          <div className="mt-2 text-sm text-gray-600 italic">
            {imageProps.caption}
          </div>
        )}
      </div>
      {children}
    </div>
  );
};

export default ImageElement;
