import { handleSetCustomersListSelectedItems } from "@/apps/Admin/Pages/Customers/ClientSideStates";
import { getTempCustomerListFilter } from "@/apps/Admin/Pages/TempCustomer/Services";
import AlertList from "@/apps/Common/AlertList";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import PageTitle from "@/apps/Common/PageTitle";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Divider, Form, Modal, Row, Typography } from "antd";
import { FC, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";

interface FormContenProps {
  fileData: any;
  saveImportedFileService: any;
  onFinish: () => void;
  type: "userList" | "customerList" | "tempCustomerList" | "autoDialer"|"fileManager";
}

const FormContent: FC<FormContenProps> = ({
  fileData,
  saveImportedFileService,
  onFinish,
  type,
}) => {
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { Text } = Typography;
  const [selectedSheet, setSelectedSheet] = useState<null | string>(null);
  const [saveFileDataList, setSaveFileDataList] = useState<any[]>([]);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [isShowErrorModal, setIsShowErrorModal] = useState(false);
  const [errorDataList, setErrorDataList] = useState([]);

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();

    const data = {
      TempFileName: fileData?.TempFileName,
      SheetName: selectedSheet,
      Mappings: saveFileDataList,
      SourceName: formValues["SourceName"],
    };
    let response: any = {};
    try {
      const fileResponse = await saveImportedFileService(data);

      if (type === "autoDialer") {
        response = await getTempCustomerListFilter({
          PageIndex: 1,
          PageSize: 100,
          CustomerSource: formValues["SourceName"],
        });
        if (response?.Value?.length > 0) {
          dispatch(
            handleSetCustomersListSelectedItems({
              selectedIds: response?.Value?.map((item: any) => item.Id),
              selectedItems: response?.Value,
            })
          );
        }
      }

      if (fileResponse?.Value?.SuccessfulRecords > 0) {
        openNotificationWithIcon(
          "success",
          t("autoDialer.list.numberSuccessfulUploads") +
            ":" +
            fileResponse?.Value?.SuccessfulRecords
        );
      }
      if (fileResponse?.Value?.FailedRecords > 0) {
        openNotificationWithIcon(
          "error",
          t("autoDialer.list.numberitemGivingErrors") +
            ":" +
            fileResponse?.Value?.FailedRecords
        );
      }
      if (fileResponse?.Value?.Errors?.length > 0) {
        const faild = fileResponse?.Value?.FailedRecords;
        const sucess = fileResponse?.Value?.SuccessfulRecords;
        await setErrorDataList(
          fileResponse?.Value?.Errors?.map((item: string, index: number) => {
            return { Description: item, faild, sucess };
          })
        );
        setIsShowErrorModal(true);
      } else {
        mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));

        setSaveFileDataList([]);
        onFinish();
      }
    } catch (errorRes: any) {
    
      const error = errorRes?.response?.data
      if (error?.errors?.length > 1 || error?.Errors?.length > 1) {
        mazakaForm.setFailed(2000, t("form.transactionFaild"));
        setErrorDataList(error?.errors || error?.Errors);
        setIsShowErrorModal(true);
      } else {
        showErrorCatching(error, mazakaForm, true, t);
      }
    }
  };

  useEffect(() => {
    if (fileData) {
      form.setFieldValue("sheets", fileData?.DefaultSheetName);
      setSelectedSheet(fileData?.DefaultSheetName);
    }
  }, [fileData]);

  useEffect(() => {
    if (!selectedSheet || !fileData?.Sheets) return;

    function normalizeTR(str: string): string {
      return str
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "")
        .replace(/ı/g, "i")
        .replace(/İ/g, "i")
        .toLowerCase();
    }

    const findSheet = fileData.Sheets.find(
      (sheet: any) =>
        normalizeTR(sheet.SheetName) === normalizeTR(selectedSheet)
    );

    if (findSheet) {
      const initializedMappings = findSheet.Columns.map((col: string) => ({
        ExcelColumnName: col,
        ModelPropertyName: "", // başta boş
      }));
      setSaveFileDataList(initializedMappings);
    }
  }, [selectedSheet, fileData]);

  return (
    <MazakaForm
      onFinish={handleOnFinish}
      form={form}
      submitButtonVisible={false}
    >
      <Row gutter={[20, 10]}>
        <MazakaSelect
          label={t("import.selectSheet")}
          placeholder={t("import.selectSheet")}
          allowClear
          name="sheets"
          xs={24}
          md={8}
          rules={[{ required: true, message: "" }]}
          onChange={(value: number, obj: any) => {
            if (value === 0 || value) {
              setSelectedSheet(obj?.label);
            } else {
              setSelectedSheet(null);
              setSaveFileDataList([]);
            }
          }}
          options={fileData?.SheetNames.map((item: any, index: number) => ({
            label: item,
            value: index,
          }))}
        />
        <MazakaInput
          label={t("import.sourceName")}
          placeholder={t("import.sourceName")}
          allowClear
          md={8}
          name="SourceName"
          xs={24}
          rules={[{ required: true, message: "" }]}
          tooltip={t("import.dataSourceName")}
        />
        <Col xs={24} md={8} className="">
          <Form.Item label="  ">
            <MazakaButton
              processType={formActions.submitProcessType}
              disabled={formActions.submitProcessType === "loading"}
              htmlType="submit"
              status="save"
            >
              {t("users.add.save")}
            </MazakaButton>
          </Form.Item>
        </Col>

        {selectedSheet && saveFileDataList.length > 0 && (
          <Col xs={24}>
            <Row gutter={[0, 10]}>
              <Col xs={24} md={8}>
                <PageTitle title={t("import.excelColumns")} isSubTitle />
              </Col>
              <Col xs={24} md={8}>
                <PageTitle title={t("import.modelFields")} isSubTitle />
              </Col>
              <Col xs={24} md={8}></Col>

              <Col xs={24}>
                <Divider className="!m-0" />
              </Col>

              <Col xs={24}>
                <Row gutter={[0, 10]}>
                  {saveFileDataList.map((mapping, index) => (
                    <Col xs={24} key={index}>
                      <Row>
                        <Col xs={24} md={8} className="!flex items-center">
                          <Text>{mapping.ExcelColumnName}</Text>
                        </Col>

                        <MazakaSelect
                          xs={24}
                          md={8}
                          placeholder="Seç"
                          options={fileData?.ModelFields?.filter(
                            (item: string) => {
                              if (item === "RowNumber") return false;
                              return !saveFileDataList.some(
                                (row, i) =>
                                  i !== index && row.ModelPropertyName === item
                              );
                            }
                          ).map((item: string) => ({
                            label: item,
                            value: item,
                          }))}
                          showSearch
                          value={mapping.ModelPropertyName || undefined}
                          allowClear
                          onChange={(selectedModelField: string) => {
                            setSaveFileDataList((prev) => {
                              const updated = [...prev];
                              updated[index] = {
                                ...updated[index],
                                ModelPropertyName: selectedModelField,
                              };
                              return updated;
                            });
                          }}
                        />
                        <Col xs={24} md={8}></Col>
                      </Row>
                      {/* ExcelColumnName - sadece gösterilecek, seçilemez */}
                    </Col>
                  ))}
                </Row>
              </Col>
              <Col xs={24} className="">
                <MazakaButton
                  processType={formActions.submitProcessType}
                  disabled={formActions.submitProcessType === "loading"}
                  htmlType="submit"
                  status="save"
                >
                  {t("users.add.save")}
                </MazakaButton>
              </Col>
            </Row>
          </Col>
        )}
      </Row>
      <Modal
        open={isShowErrorModal}
        onCancel={() => {
          setIsShowErrorModal(false);
        }}
        footer={false}
      >
        <AlertList data={errorDataList} />
      </Modal>
    </MazakaForm>
  );
};

export default FormContent;
