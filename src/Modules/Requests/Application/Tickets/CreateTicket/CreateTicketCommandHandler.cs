using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Domain;
using Shared.Application;
using Shared.Contracts;
using Shared.Domain;
using Shared.Utilities;

namespace Requests.Application.Tickets.CreateTicket;

public class CreateTicketCommandHandler(
    IRequestsDbContext dbContext,
    IWorkContext workContext,
    ISharedDepartmentService departmentService,
    ITicketHistoryService historyService,
    AppSettings appSettings
) : IRequestHandler<CreateTicketCommand, Result<TicketDto>>
{
    private readonly IRequestsDbContext _dbContext = dbContext;
    private readonly IWorkContext _workContext = workContext;
    private readonly ISharedDepartmentService _departmentService = departmentService;
    private readonly ITicketHistoryService _historyService = historyService;
    private readonly AppSettings _appSettings = appSettings;

    public async Task<Result<TicketDto>> Handle(CreateTicketCommand request, CancellationToken cancellationToken)
    {
        var ticketId = Guid.NewGuid();
        var ticketCode = CodeGenerator.ProcessFormat(_appSettings.TicketCodeFormat, "Ticket");
        Guid? statusId = null;
        var subject = await _dbContext.TicketSubjects.FindAsync([request.SubjectId], cancellationToken);
        if (subject != null && subject.FlowId.HasValue)
        {
            var flow = await _dbContext.Flows.Include(x => x.Nodes).FirstOrDefaultAsync(f => f.Id == subject.FlowId.Value, cancellationToken);
            statusId = flow.Nodes.FirstOrDefault(n => n.NodeType == NodeType.Start)?.Id;
        }
        var ticket = new Ticket
        {
            Id = ticketId,
            Code = ticketCode,
            TicketType = request.TicketType,
            TopTicketId = request.TopTicketId,
            SubjectId = request.SubjectId,
            CustomerId = request.CustomerId,
            CallId = request.CallId,
            ChatId = request.ChatId,
            Title = request.Title,
            Description = request.Description ?? "",
            NotificationWayId = request.NotificationWayId,
            UserId = request.UserId,
            ReporterUserId = _workContext.UserId,
            Priority = request.Priority ?? PriorityEnum.Medium,
            EndDate = request.EndDate,
            Watchlist = request.Watchlist ?? [],
            Tags = request.Tags ?? [],
            Country = request.Country,
            State = request.State,
            City = request.City,
            Province = request.Province,
            Detail = request.Detail,
            AttributeData = request.AttributeData,
            StatusId = statusId
        };
        if (request.UserId.HasValue)
        {
            ticket.Watchlist.Add(request.UserId.Value);
        }
        ticket.Watchlist.Add(ticket.ReporterUserId);
        ticket.Watchlist = [.. ticket.Watchlist.Distinct()];

        // Dosyaları ekle
        if (request.TicketFiles?.Count > 0)
        {
            ticket.TicketFiles = request.TicketFiles.Select(f => new TicketFile
            {
                Id = Guid.NewGuid(),
                TicketId = ticketId,
                FileId = f.FileId,
                FileName = f.FileName,
                FilePath = f.FilePath
            }).ToList();
        }

        _dbContext.Tickets.Add(ticket);
        if (request.DepartmentIds?.Any() == true)
        {
            var departments = (await _departmentService.GetDepartmentsByIdsAsync(request.DepartmentIds)).ToDictionary(x => x.Id, x => x.Name);
            foreach (var departmentId in request.DepartmentIds)
            {
                _dbContext.TicketDepartments.Add(new TicketDepartment
                {
                    Id = Guid.NewGuid(),
                    TicketId = ticket.Id,
                    DepartmentId = departmentId,
                    DepartmentName = departments.TryGetValue(departmentId, out string? value) ? value : "Bilinmeyen Departman",
                });
            }
        }

        // History tracking
        await _historyService.TrackTicketCreatedAsync(ticket.Id, ticket, cancellationToken);

        await _dbContext.SaveChangesAsync(cancellationToken);
        var ticketDto = new TicketDto
        {
            Id = ticket.Id,
            Code = ticket.Code,
            TicketType = ticket.TicketType,
            TopTicketId = ticket.TopTicketId,
            SubjectId = ticket.SubjectId,
            SubjectName = subject?.Name,
            CustomerId = ticket.CustomerId,
            CallId = ticket.CallId,
            ChatId = ticket.ChatId,
            Title = ticket.Title,
            Description = ticket.Description,
            NotificationWayId = ticket.NotificationWayId,
            UserId = ticket.UserId,
            ReporterUserId = ticket.ReporterUserId,
            Priority = ticket.Priority,
            StatusId = ticket.StatusId,
            StatusName = ticket.Status?.Name,
            StatusType = ticket.Status.NodeType,
            EndDate = ticket.EndDate,
            Watchlist = ticket.Watchlist,
            Tags = ticket.Tags,
            Country = ticket.Country,
            State = ticket.State,
            City = ticket.City,
            Province = ticket.Province,
            Detail = ticket.Detail,
            AttributeData = ticket.AttributeData,
            InsertDate = ticket.InsertDate,
            UpdateDate = ticket.UpdateDate
        };
        return Result.Success(ticketDto);
    }
}
