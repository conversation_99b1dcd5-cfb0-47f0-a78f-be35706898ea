using System.Globalization;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace Conversations.Application.Channels.WhatsApp;

public partial record WhatsAppDto
{
    public string Object { get; set; }
    public WhatsappEntryDto[] Entry { get; set; }

    public WhatsAppWebhookRequest ToWebhookRequest()
    {
        if (Entry[0].Changes[0].Value?.Messages?.Length > 0)
        {
            var profileName = MyRegex().Replace(Entry[0].Changes[0].Value.Contacts[0].Profile.Name, m => ((char)Convert.ToInt32(m.Groups[1].Value, 16)).ToString());
            var content = string.Empty;
            var contentType = Entry[0].Changes[0].Value.Messages[0].Type;
            var contentId = string.Empty;
            if (contentType == "text")
            {
                content = Entry[0].Changes[0].Value.Messages[0].Text?.Body;
            }
            else if (contentType == "image")
            {
                content = Entry[0].Changes[0].Value.Messages[0].Image?.Caption;
                contentId = Entry[0].Changes[0].Value.Messages[0].Image?.Id;
            }
            else if (contentType == "sticker")
            {
                contentId = Entry[0].Changes[0].Value.Messages[0].Sticker?.Id;
            }
            else if (contentType == "video")
            {
                content = Entry[0].Changes[0].Value.Messages[0].Video?.Caption;
                contentId = Entry[0].Changes[0].Value.Messages[0].Video?.Id;
            }
            else if (contentType == "audio")
            {
                contentId = Entry[0].Changes[0].Value.Messages[0].Audio?.Id;
            }
            else if (contentType == "document")
            {
                content = Entry[0].Changes[0].Value.Messages[0].Document?.Filename;
                contentId = Entry[0].Changes[0].Value.Messages[0].Document?.Id;
            }
            else if (contentType == "location")
            {
                content += Entry[0].Changes[0].Value.Messages[0].Location?.Latitude?.ToString(CultureInfo.InvariantCulture) + ",";
                content += Entry[0].Changes[0].Value.Messages[0].Location?.Longitude?.ToString(CultureInfo.InvariantCulture) + "";
            }
            else if (contentType == "unsupported")
            {
                content += Entry[0].Changes[0].Value.Messages[0].Errors?.Code + " - ";
                content += Entry[0].Changes[0].Value.Messages[0].Errors?.Title + " - ";
                content += Entry[0].Changes[0].Value.Messages[0].Errors?.Message;
            }
            return new WhatsAppWebhookRequest()
            {
                Type = Entry[0].Changes[0].Field,
                BaseChatId = Entry[0].Changes[0].Value.Metadata.PhoneNumberId,
                CustomerPhone = Entry[0].Changes[0].Value.Contacts[0].WaId,
                CustomerName = profileName,
                From = Entry[0].Changes[0].Value.Messages[0].From,
                To = Entry[0].Changes[0].Value.Messages[0].To,
                MessageId = Entry[0].Changes[0].Value.Messages[0].Id,
                Timestamp = DateTimeOffset.FromUnixTimeMilliseconds(long.Parse(Entry[0].Changes[0].Value.Messages[0].Timestamp)).UtcDateTime,
                ContentType = contentType,
                Content = content,
                ContentId = contentId,
                MetaData = new Dictionary<string, object>
                {
                    { "Contacts.Profile.Name", Entry[0].Changes[0].Value.Contacts[0].Profile.Name },
                    { "display_phone_number", Entry[0].Changes[0].Value.Metadata.DisplayPhoneNumber },
                    { "phone_number_id", Entry[0].Changes[0].Value.Metadata.PhoneNumberId }
                }
            };
        }
        else if (Entry[0].Changes[0].Value?.Statuses?.Length > 0)
        {
            return new WhatsAppWebhookRequest()
            {
                Type = "status",
                BaseChatId = Entry[0].Changes[0].Value.Metadata.PhoneNumberId,
                MessageId = Entry[0].Changes[0].Value.Statuses[0].Id,
                Status = Entry[0].Changes[0].Value.Statuses[0].Status,
                Timestamp = DateTimeOffset.FromUnixTimeMilliseconds(long.Parse(Entry[0].Changes[0].Value.Statuses[0].Timestamp)).UtcDateTime
            };
        }
        else
        {
            return null;
        }
    }

    [GeneratedRegex(@"\\u([0-9A-Fa-f]{4})")]
    private static partial Regex MyRegex();
}

public record WhatsappEntryDto
{
    public string Id { get; set; }
    public WhatsappChangeDto[] Changes { get; set; }
}

public record WhatsappChangeDto
{
    public string Field { get; set; }
    public WhatsappValueDto Value { get; set; }
}

public record WhatsappValueDto
{
    [JsonPropertyName("messaging_product")]
    public string MessagingProduct { get; set; }
    public WhatsappMetadataDto Metadata { get; set; }
    public WhatsappContactDto[] Contacts { get; set; }
    public WhatsappStatusDto[] Statuses { get; set; }
    public WhatsappMessageDto[] Messages { get; set; }
}

public record WhatsappMessageDto
{
    public string From { get; set; }
    public string To { get; set; }
    public string Id { get; set; }
    public string Timestamp { get; set; }
    public string Type { get; set; }
    public WhatsappTextDto? Text { get; set; }
    public WhatsappImageDto? Image { get; set; }
    public WhatsappStickerDto? Sticker { get; set; }
    public WhatsappAudioDto? Audio { get; set; }
    public WhatsappVideoDto? Video { get; set; }
    public WhatsappDocumentDto? Document { get; set; }
    public WhatsappLocationDto? Location { get; set; }
    public WhatsappErrorsDto? Errors { get; set; }
}

public record WhatsappErrorsDto
{
    public string Code { get; set; }
    public string Title { get; set; }
    public string Message { get; set; }
}

public record WhatsappTextDto
{
    public string Body { get; set; }
}
public record WhatsappLocationDto
{
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
}
public record WhatsappDocumentDto
{
    public string Filename { get; set; }
    [JsonPropertyName("mime_type")]
    public string MimeType { get; set; }
    public string Sha256 { get; set; }
    public string Id { get; set; }
}
public record WhatsappImageDto
{
    [JsonPropertyName("mime_type")]
    public string MimeType { get; set; }
    public string Sha256 { get; set; }
    public string Caption { get; set; }
    public string Id { get; set; }
}
public record WhatsappStickerDto
{
    [JsonPropertyName("mime_type")]
    public string MimeType { get; set; }
    public string Sha256 { get; set; }
    public string Id { get; set; }
    public bool Animated { get; set; }
}
public record WhatsappVideoDto
{
    [JsonPropertyName("mime_type")]
    public string MimeType { get; set; }
    public string Sha256 { get; set; }
    public string Caption { get; set; }
    public string Id { get; set; }
}
public record WhatsappAudioDto
{
    [JsonPropertyName("mime_type")]
    public string MimeType { get; set; }
    public string Sha256 { get; set; }
    public string Id { get; set; }
    public bool Voice { get; set; }
}

public record WhatsappContactDto
{
    [JsonPropertyName("wa_id")]
    public string WaId { get; set; }
    public WhatsappProfileDto Profile { get; set; }
}

public record WhatsappStatusDto
{
    public string Id { get; set; }
    public string Status { get; set; }
    public string Timestamp { get; set; }
}

public record WhatsappProfileDto
{
    public string Name { get; set; }
}

public record WhatsappMetadataDto
{
    [JsonPropertyName("display_phone_number")]
    public string DisplayPhoneNumber { get; set; }
    [JsonPropertyName("phone_number_id")]
    public string PhoneNumberId { get; set; }
}
