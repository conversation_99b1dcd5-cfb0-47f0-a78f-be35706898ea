using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Domain;
using Users.Application.Abstractions;

namespace Users.Application.Management.ListUsers;

internal sealed class ListUsersQueryHandler(
    IUserDbContext context,
    IEventBus eventBus
) : IRequestHandler<ListUsersQuery, PagedResult<ListUsersResponse>>
{

    public async Task<PagedResult<ListUsersResponse>> Handle(
        ListUsersQuery request,
        CancellationToken cancellationToken)
    {
        // await eventBus.PublishAsync(new NotificationSendedEvent(
        //         Guid.Parse("9347150e-6d01-4c6e-f833-08dd9f58d715"),
        //         "Notification sms Test",
        //         "Notification sms Test açıklama",
        //         "TicketComment",
        //         "{}",
        //         ForceSMS: true
        //     ), cancellationToken);
        var query = context.Users.AsNoTracking();

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(u =>
                u.Email.ToLower().Contains(searchTerm) ||
                u.Name.ToLower().Contains(searchTerm) ||
                u.Surname.ToLower().Contains(searchTerm) ||
                u.PhoneNumber.Contains(searchTerm));
        }
        if (!string.IsNullOrWhiteSpace(request.Email))
        {
            query = query.Where(u => u.Email.ToLower().Contains(request.Email.ToLower()));
        }
        if (!string.IsNullOrWhiteSpace(request.Extension))
        {
            query = query.Where(u => u.ThreeCXExtension.ToLower().Contains(request.Extension.ToLower()));
        }
        if (request.Ids != null && request.Ids.Length > 0)
        {
            query = query.Where(u => request.Ids.Contains(u.Id));
        }
        if (request.DepartmentId.HasValue)
        {
            query = query.Where(u => u.UserDepartment.Any(ud => ud.DepartmentId == request.DepartmentId.Value));
        }
        if (request.Active.HasValue)
        {
            query = query.Where(u => u.Active == request.Active.Value);
        }
        var filteredCount = await query.CountAsync(cancellationToken);
        var totalCount = await context.Users.CountAsync(cancellationToken);
        var items = await query
            .OrderByDescending(u => u.InsertDate)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(u => new ListUsersResponse(
                u.Id,
                u.Email,
                u.Name,
                u.Surname,
                u.PhoneNumber ?? string.Empty,
                u.PhonePrefix,
                u.ThreeCXExtension,
                u.ThreeCXEnabled,
                u.ThreeCXExternal,
                u.ThreeCXRecording,
                u.ChatURL,
                u.Active,
                u.InsertDate,
                u.UpdateDate,
                u.UserDepartment.Select(x => x.Department.Name).ToList()))
            .ToListAsync(cancellationToken);
        return new PagedResult<ListUsersResponse>(items)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            FilteredCount = filteredCount,
            Count = totalCount,
        };
    }
}
