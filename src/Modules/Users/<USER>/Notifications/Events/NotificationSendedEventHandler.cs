using MediatR;
using Microsoft.AspNetCore.SignalR;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Domain;
using Users.Application.Abstractions;
using Users.Application.Notifications.Hubs;
using Users.Domain.Notifications;

namespace Users.Application.Notifications.Events;

internal sealed class NotificationSendedEventHandler(
    IHubContext<NotificationHub> hubContext,
    IUserDbContext dbContext,
    IEventBus eventBus,
    IWorkContext workContext
) : INotificationHandler<NotificationSendedEvent>
{
    public async Task Handle(
        NotificationSendedEvent notificationEvent,
        CancellationToken cancellationToken)
    {
        if (workContext.UserId == notificationEvent.UserId)
        {
            return;
        }
        var user = await dbContext.Users.FindAsync([notificationEvent.UserId], cancellationToken: cancellationToken);
        if (user == null)
        {
            return;
        }
        var notification = new Notification
        {
            UserId = notificationEvent.UserId,
            Title = notificationEvent.Title,
            Message = notificationEvent.Message,
            Type = notificationEvent.Type,
            Data = notificationEvent.Data,
            InsertDate = DateTime.Now
        };
        dbContext.Notification.Add(notification);
        await dbContext.SaveChangesAsync(cancellationToken);
        await hubContext.Clients
            .User(notificationEvent.UserId.ToString())
            .SendAsync("ReceiveMessage", new
            {
                notificationEvent.Title,
                notificationEvent.Message,
                notificationEvent.Type,
                notificationEvent.Data
            }, cancellationToken: cancellationToken);
        if ((notificationEvent.ForceEmail || user.EmailPermission) && !string.IsNullOrWhiteSpace(user.Email))
        {
            await eventBus.PublishAsync(new EmailSendedEvent(
                user.Email,
                notificationEvent.Title,
                notificationEvent.Message
            ), cancellationToken);
            notification.EmailSended = true;
        }
        if ((notificationEvent.ForceSMS || user.SMSPermission) && !string.IsNullOrWhiteSpace(user.PhoneNumber))
        {
            await eventBus.PublishAsync(new SmsSendedEvent(
                user.PhoneNumber,
                notificationEvent.Message
            ), cancellationToken);
            notification.SmsSended = true;
        }
        if ((notificationEvent.ForceMobilePush || user.MobilePushPermission) && !string.IsNullOrWhiteSpace(user.MobileNotificationToken))
        {
            await eventBus.PublishAsync(new MobilePushSendedEvent(
                user.MobileNotificationToken,
                notificationEvent.Title,
                notificationEvent.Message,
                notificationEvent.Data
            ), cancellationToken);
            notification.MobilePushSended = true;
        }
        if ((notificationEvent.ForceWebPush || user.WebPushPermission) && !string.IsNullOrWhiteSpace(user.WebNotificationToken))
        {
            await eventBus.PublishAsync(new WebPushSendedEvent(
                user.WebNotificationToken,
                notificationEvent.Title,
                notificationEvent.Message,
                notificationEvent.Data
            ), cancellationToken);
            notification.WebPushSended = true;
        }
        await dbContext.SaveChangesAsync(cancellationToken);
    }
}
